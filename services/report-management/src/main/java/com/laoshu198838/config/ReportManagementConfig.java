package com.laoshu198838.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 报表管理模块配置类
 * 确保报表管理相关的服务类能被Spring Boot正确扫描和注册
 * 
 * <AUTHOR>
 */
@Configuration
@EnableTransactionManagement
@ComponentScan(basePackages = {
    "com.laoshu198838.service",
    "com.laoshu198838.validator",
    "com.laoshu198838.exception"
})
public class ReportManagementConfig {
    
    // 配置类可以在这里添加特定的Bean配置
    
}
