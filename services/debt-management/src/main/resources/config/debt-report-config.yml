# 债权报表配置文件
# 定义各种债权相关报表的配置信息

reports:
  # 简单报表配置
  new-debt-details:
    name: "新增债权明细表"
    type: "SIMPLE"
    template: "新增债权明细表模板.xlsx"
    datasource: "debt-management"
    description: "导出新增债权的详细信息"
    columns:
      - name: "序号"
        field: "sequence"
        type: "INTEGER"
        width: 50
      - name: "债权人"
        field: "creditor"
        type: "STRING"
        width: 150
        required: true
      - name: "债务人"
        field: "debtor"
        type: "STRING"
        width: 150
        required: true
      - name: "案件名称"
        field: "caseName"
        type: "STRING"
        width: 200
      - name: "年初余额"
        field: "yearStartBalance"
        type: "DECIMAL"
        format: "#,##0.00"
        width: 100
      - name: "当月余额"
        field: "currentMonthBalance"
        type: "DECIMAL"
        format: "#,##0.00"
        width: 100
      - name: "累计清收"
        field: "cumulativeRecovery"
        type: "DECIMAL"
        format: "#,##0.00"
        width: 100

  reduction-debt-details:
    name: "处置债权明细表"
    type: "SIMPLE"
    template: "处置债权明细表模板.xlsx"
    datasource: "debt-management"
    description: "导出处置债权的详细信息"
    columns:
      - name: "序号"
        field: "sequence"
        type: "INTEGER"
        width: 50
      - name: "债权人"
        field: "creditor"
        type: "STRING"
        width: 150
        required: true
      - name: "债务人"
        field: "debtor"
        type: "STRING"
        width: 150
        required: true
      - name: "案件名称"
        field: "caseName"
        type: "STRING"
        width: 200
      - name: "处置方式"
        field: "disposalMethod"
        type: "STRING"
        width: 100
      - name: "处置金额"
        field: "disposalAmount"
        type: "DECIMAL"
        format: "#,##0.00"
        width: 100

  # 复杂报表配置
  overdue-debt-complex:
    name: "逾期债权清收统计表"
    type: "COMPLEX"
    template: "逾期债权清收统计表（模版）.xlsx"
    datasource: "overdue-debt"
    description: "包含8个子表的完整逾期债权统计报表"
    tables:
      - name: "表3-涉诉"
        database_table: "诉讼表"
        excel_sheet: "表3-涉诉"
        export_method: "exportToTable3"
        parameters:
          start_row: 4
          search_column: 1
      - name: "表4-非涉诉"
        database_table: "非诉讼表"
        excel_sheet: "表4-非涉诉"
        export_method: "exportToTable4"
        parameters:
          start_row: 4
          search_column: 1
      - name: "表5-减值准备"
        database_table: "减值准备表"
        excel_sheet: "表5-减值准备"
        export_method: "exportToTable5"
        parameters:
          start_row: 4
          search_column: 1
          special_handling: true  # 需要特殊处理长投工作表
      - name: "表7-10万元及以下应收债权明细表"
        database_table: "减值准备表"
        excel_sheet: "表7-10万元及以下应收债权明细表"
        export_method: "exportToTable7"
        parameters:
          start_row: 4
          search_column: 1
          amount_limit: true  # 需要金额限制参数
      - name: "表8-临3表"
        database_table: "处置表"
        excel_sheet: "表8-临3表"
        export_method: "exportToTable8"
        parameters:
          start_row: 4
          search_column: 1
      - name: "表9-新增逾期债权明细表"
        database_table: "减值准备表"
        excel_sheet: "表9-新增逾期债权明细表"
        export_method: "exportToTable9"
        parameters:
          start_row: 4
          search_column: 1
          complex_sql: true  # 使用复杂SQL查询
      - name: "表10-债权处置明细表"
        database_table: "处置表"
        excel_sheet: "表10-债权处置明细表"
        export_method: "exportToTable10"
        parameters:
          start_row: 4
          search_column: 1
    output:
      format: "XLSX"
      filename_template: "{year}年{month:02d}月逾期债权清收统计表-万润科技汇总.xlsx"
      encoding: "UTF-8"
      compress: false

# 全局配置
global:
  default_start_row: 4
  default_search_column: 1
  date_format: "yyyy-MM-dd"
  number_format: "#,##0.00"
  template_path: "classpath:templates/"
  
# 数据源配置
datasources:
  debt-management:
    connection_name: "逾期债权数据库"
    timeout: 30000
  overdue-debt:
    connection_name: "逾期债权数据库"
    timeout: 60000  # 复杂查询需要更长超时时间
