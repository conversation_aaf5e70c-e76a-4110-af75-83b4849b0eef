package com.laoshu198838.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

/**
 * 债权导出服务
 * 专门负责债权相关Excel导出功能
 * 
 * <AUTHOR>
 */
@Service
public class DebtExportService {
    
    private static final Logger logger = LoggerFactory.getLogger(DebtExportService.class);
    
    @Autowired
    private ExcelExportService excelExportService;
    
    /**
     * 导出新增债权明细
     */
    public ResponseEntity<byte[]> exportNewDebtDetails(String year, String month, String company) {
        logger.info("导出新增债权明细: year={}, month={}, company={}", year, month, company);
        return excelExportService.exportNewDebtDetails(year, month, company);
    }
    
    /**
     * 导出处置债权明细
     */
    public ResponseEntity<byte[]> exportReductionDebtDetails(String year, String month, String company) {
        logger.info("导出处置债权明细: year={}, month={}, company={}", year, month, company);
        return excelExportService.exportReductionDebtDetails(year, month, company);
    }
    
    /**
     * 导出完整的逾期债权清收统计表
     */
    public ResponseEntity<byte[]> exportCompleteOverdueReport(String year, String month, String amount) {
        logger.info("导出完整逾期债权报表: year={}, month={}, amount={}", year, month, amount);
        return excelExportService.exportCompleteOverdueReport(year, month, amount);
    }
}