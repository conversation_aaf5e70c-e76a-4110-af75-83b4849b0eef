package com.laoshu198838.service;

import com.laoshu198838.entity.overdue_debt.ImpairmentReserve;
import com.laoshu198838.entity.overdue_debt.LitigationClaim;
import com.laoshu198838.entity.overdue_debt.NonLitigationClaim;
import com.laoshu198838.entity.overdue_debt.OverdueDebtDecrease;
import com.laoshu198838.model.overduedebt.dto.query.DebtDecreaseDTO;
import com.laoshu198838.repository.overdue_debt.ImpairmentReserveRepository;
import com.laoshu198838.repository.overdue_debt.LitigationClaimRepository;
import com.laoshu198838.repository.overdue_debt.NonLitigationClaimRepository;
import com.laoshu198838.repository.overdue_debt.OverdueDebtDecreaseRepository;
import com.laoshu198838.repository.overdue_debt.DebtDecreaseQueryRepository;
import com.laoshu198838.util.data.MapConvertUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;


/**
 * 债权处置业务逻辑层
 *
 * <AUTHOR>
 */
@Service
public class OverdueDebtDecreaseService {

    private static final Logger logger = LoggerFactory.getLogger(OverdueDebtDecreaseService.class);
    @Autowired
    private final LitigationClaimRepository litigationClaimRepository;
    @Autowired
    private final OverdueDebtDecreaseRepository overdueDebtDecreaseRepository;
    @Autowired
    private final NonLitigationClaimRepository nonLitigationClaimRepository;
    @Autowired
    private final ImpairmentReserveRepository impairmentReserveRepository;
    @Autowired
    private final DebtDecreaseQueryRepository debtDecreaseQueryRepository;

    public OverdueDebtDecreaseService(OverdueDebtDecreaseRepository overdueDebtDecreaseRepository,
                                      LitigationClaimRepository litigationClaimRepository,
                                      NonLitigationClaimRepository nonLitigationClaimRepository,
                                      ImpairmentReserveRepository impairmentReserveRepository,
                                      DebtDecreaseQueryRepository debtDecreaseQueryRepository) {
        this.overdueDebtDecreaseRepository = overdueDebtDecreaseRepository;
        this.litigationClaimRepository = litigationClaimRepository;
        this.nonLitigationClaimRepository = nonLitigationClaimRepository;
        this.impairmentReserveRepository = impairmentReserveRepository;
        this.debtDecreaseQueryRepository = debtDecreaseQueryRepository;
    }

    /**
     * 更新债权处置信息
     * <p>
     * 该方法处理前端提交的债权处置数据，并将其同步更新到多个相关表中。
     * 处理流程如下：
     * 1. 首先检查联合主键信息是否完整
     * 2. 更新债权处置表数据
     * 3. 更新减值准备表数据
     * 4. 根据是否涉诉字段，选择更新诉讼表或非诉讼表
     * </p>
     *
     * @param inputData 前端传入的处置信息，使用JSON格式的Map对象
     * @return ResponseEntity 包含处理结果的HTTP响应对象
     */
    @Transactional
    public ResponseEntity<?> updateDebtReductionData(Map<String, Object> inputData) {
        logger.info("原始前端数据: {}", inputData);

        // 转换前端数据格式为内部处理格式
        Map<String, Object> reductionData = new HashMap<>();

        // 构建id对象（包含联合主键信息）
        Map<String, Object> idMap = new HashMap<>();

        // 检查前端数据是否包含嵌套的id对象
        if (inputData.containsKey("id") && inputData.get("id") instanceof Map) {
            // 前端传递的是嵌套结构
            @SuppressWarnings("unchecked")
            Map<String, Object> frontendIdMap = (Map<String, Object>) inputData.get("id");
            idMap.put("creditor", frontendIdMap.get("creditor"));
            idMap.put("debtor", frontendIdMap.get("debtor"));
            idMap.put("period", frontendIdMap.get("period"));
            idMap.put("isLitigation", frontendIdMap.get("isLitigation"));
            idMap.put("year", frontendIdMap.get("year"));
            idMap.put("month", frontendIdMap.get("month"));
        } else {
            // 兼容扁平化结构
            idMap.put("creditor", inputData.get("creditor"));
            idMap.put("debtor", inputData.get("debtor"));
            idMap.put("period", inputData.get("period") != null ? inputData.get("period") : inputData.get("debtPeriod"));
            idMap.put("isLitigation", inputData.get("isLitigation"));

            // 解析yearMonth获取年份和月份
            String yearMonth = (String) inputData.get("yearMonth");
            if (yearMonth != null && yearMonth.contains("-")) {
                String[] parts = yearMonth.split("-");
                idMap.put("year", Integer.parseInt(parts[0]));
                idMap.put("month", Integer.parseInt(parts[1]));
            }
        }

        reductionData.put("id", idMap);

        // 处理处置金额
        if (inputData.containsKey("dispositionAmount")) {
            reductionData.put("monthlyReduceAmount", inputData.get("dispositionAmount"));
        } else if (inputData.containsKey("currentMonthDisposeDebt")) {
            reductionData.put("monthlyReduceAmount", inputData.get("currentMonthDisposeDebt"));
        }

        // 处理处置明细
        if (inputData.containsKey("dispositionDetails")) {
            @SuppressWarnings("unchecked")
            Map<String, Object> details = (Map<String, Object>) inputData.get("dispositionDetails");

            // 正确处理现金处置
            if (details.containsKey("cashDisposal")) {
                reductionData.put("cashDisposal", details.get("cashDisposal"));
            } else if (details.containsKey("cashAmount")) {
                reductionData.put("cashDisposal", details.get("cashAmount"));
            }

            // 正确处理分期还款
            if (details.containsKey("installmentRepayment")) {
                reductionData.put("installmentRepayment", details.get("installmentRepayment"));
            } else if (details.containsKey("installmentAmount")) {
                reductionData.put("installmentRepayment", details.get("installmentAmount"));
            }

            // 正确处理资产抵债
            if (details.containsKey("assetDebt")) {
                reductionData.put("assetDebt", details.get("assetDebt"));
            } else if (details.containsKey("assetAmount")) {
                reductionData.put("assetDebt", details.get("assetAmount"));
            }

            // 处理其他方式（合并账务调整和其他）
            BigDecimal otherWaysAmount = BigDecimal.ZERO;

            // 处理账务调整
            if (details.containsKey("adjustmentAmount")) {
                Object adjustmentValue = details.get("adjustmentAmount");
                if (adjustmentValue != null && !adjustmentValue.toString().isEmpty()) {
                    otherWaysAmount = otherWaysAmount.add(getBigDecimal(adjustmentValue));
                }
            }

            // 处理其他金额
            if (details.containsKey("otherAmount")) {
                Object otherValue = details.get("otherAmount");
                if (otherValue != null && !otherValue.toString().isEmpty()) {
                    otherWaysAmount = otherWaysAmount.add(getBigDecimal(otherValue));
                }
            } else if (details.containsKey("otherWays")) {
                Object otherValue = details.get("otherWays");
                if (otherValue != null && !otherValue.toString().isEmpty()) {
                    otherWaysAmount = otherWaysAmount.add(getBigDecimal(otherValue));
                }
            }

            // 设置其他方式总额
            reductionData.put("otherWays", otherWaysAmount);

            logger.info("处置明细处理结果: 现金={}\u5206期={}\u62b5债={}\u5176他={}(合并后)",
                    reductionData.get("cashDisposal"),
                    reductionData.get("installmentRepayment"),
                    reductionData.get("assetDebt"),
                    reductionData.get("otherWays"));
        }

        // 复制其他字段
        if (inputData.containsKey("managementCompany")) {
            reductionData.put("managementCompany", inputData.get("managementCompany"));
        }
        if (inputData.containsKey("remark")) {
            reductionData.put("remark", inputData.get("remark"));
        }

        logger.info("转换后的处理数据: {}", reductionData);

        // 验证联合主键字段是否完整
        if (!idMap.containsKey("isLitigation")) {
            return ResponseEntity.badRequest().body("缺少 isLitigation 字段");
        }

        // 如果 isLitigation 为 null，设置默认值为 "否"
        if (idMap.get("isLitigation") == null) {
            idMap.put("isLitigation", "否");
            logger.info("isLitigation 字段为 null，已设置默认值为 '否'");
        }

        // 获取是否涉诉字段值，用于后续判断处理流程
        String isLitigation = String.valueOf(idMap.get("isLitigation")).trim();

        // 1、将数据更新到债权处置表
        updateOverdueDebtDecreaseTable(reductionData);

        // 2、将数据更新到减值准备表
        impairmentReserveTable(reductionData);

        // 根据是否涉诉字段决定后续处理流程
        if ("是".equals(isLitigation)) {
            // 3、如果是涉诉债权，将数据更新到诉讼表
            logger.info("3、如果是涉诉债权");
            return updateLitigationClaimTable(reductionData);
        } else {
            // 4、如果是非涉诉债权，将数据更新到非诉讼表
            logger.info("4、如果是非涉诉债权");
            logger.info("更新非诉讼表: {}", reductionData);
            return updateNonLitigationClaimTable(reductionData);
        }
    }

    /**
     * 将处置表数据同步更新到非诉讼表
     * 更新非诉讼表中的本月处置金额，并相应减少本月本金增减，更新本月末本金
     *
     * @param reductionData 前端传入的处置信息json
     */
    @Transactional
    public void updateOverdueDebtDecreaseTable(Map<String, Object> reductionData) {
        logger.info("更新债权处置表: {}", reductionData.toString());

        try {
            // 添加更新时间
            reductionData.put("updateTime", LocalDateTime.now());

            // 转换为实体
            OverdueDebtDecrease incomingEntity = MapConvertUtil.convert(reductionData, OverdueDebtDecrease.class);
            
            // 检查转换后的实体
            if (incomingEntity == null) {
                logger.error("MapConvertUtil转换失败，返回null");
                throw new RuntimeException("数据转换失败");
            }
            
            OverdueDebtDecrease.OverdueDebtDecreaseKey key = incomingEntity.getId();
            
            // 确保主键不为null
            if (key == null) {
                logger.error("转换后的实体主键为null");
                throw new RuntimeException("主键数据转换失败");
            }

            // 确保 isLitigation 字段不为 null
            if (key.getIsLitigation() == null) {
                key.setIsLitigation("否");
                logger.info("在 updateOverdueDebtDecreaseTable 中设置默认的 isLitigation 值为 '否'");
            }

            // 根据主键查询是否已有记录
            Optional<OverdueDebtDecrease> optionalExisting = overdueDebtDecreaseRepository.findById(key);

            if (optionalExisting.isPresent()) {
                OverdueDebtDecrease existingEntity = optionalExisting.get();
                mergeEntity(existingEntity, incomingEntity); // ✅ 合并逻辑
                overdueDebtDecreaseRepository.save(existingEntity);
            } else {
                // 不存在记录，直接保存新数据
                overdueDebtDecreaseRepository.save(incomingEntity);
            }
            
        } catch (Exception e) {
            logger.error("更新债权处置表时发生错误", e);
            logger.error("失败的数据: {}", reductionData);
            throw new RuntimeException("更新债权处置表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将处置表数据同步更新到非诉讼表
     * 更新非诉讼表中的本月处置金额，并相应减少本月本金增减，更新本月末本金
     * 如果处置的是过去月份的债权，会同步更新后续月份的上月末本金和本月末本金
     *
     * @param reductionData 前端传入的处置信息DTO
     */
    @Transactional
    public ResponseEntity<?> updateNonLitigationClaimTable(Map<String, Object> reductionData) {
        @SuppressWarnings("unchecked")
        Map<String, Object> idMap = (Map<String, Object>) reductionData.get("id");
        idMap.remove("isLitigation"); // 清理非主键字段

        // 移除caseName字段，因为非诉讼表中没有这个字段
        if (reductionData.containsKey("caseName")) {
            reductionData.remove("caseName");
            logger.info("移除caseName字段，因为非诉讼表中不存在该字段");
        }

        NonLitigationClaim newEntity = MapConvertUtil.convert(reductionData, NonLitigationClaim.class);
        NonLitigationClaim.NonLitigationCompositeKey key = newEntity.getId();

        // 确保主键字段不为 null
        if (key.getCreditor() == null || key.getDebtor() == null || key.getPeriod() == null) {
            logger.error("非诉讼表主键字段不完整: creditor={}, debtor={}, period={}",
                         key.getCreditor(), key.getDebtor(), key.getPeriod());
            return ResponseEntity.badRequest().body("非诉讼表主键字段不完整");
        }

        BigDecimal reduceAmount = getBigDecimal(reductionData.get("monthlyReduceAmount"));

        Optional<NonLitigationClaim> optional = nonLitigationClaimRepository.findById(key);
        NonLitigationClaim existingOrNew;

        if (optional.isPresent()) {
            NonLitigationClaim existing = optional.get();

            // ✅ 累加更新
            existing.setCurrentMonthPrincipalIncreaseDecrease(
                    subtract(existing.getCurrentMonthPrincipalIncreaseDecrease(), reduceAmount));
            existing.setCurrentMonthPrincipal(
                    subtract(existing.getCurrentMonthPrincipal(), reduceAmount));
            existing.setAnnualCumulativeRecovery(
                    sum(existing.getAnnualCumulativeRecovery(), reduceAmount));
            existing.setCurrentMonthDisposedDebt(
                    sum(existing.getCurrentMonthDisposedDebt(), reduceAmount));

            // 可选字段覆盖
            if (newEntity.getManagementCompany() != null) {
                existing.setManagementCompany(newEntity.getManagementCompany());
            }
            if (newEntity.getRemark() != null) {
                existing.setRemark(newEntity.getRemark());
            }

            nonLitigationClaimRepository.save(existing);
            existingOrNew = existing;

        } else {
            // 若不存在，则直接构建新记录（不推荐生产环境直接 insert，最好预先初始化）
            newEntity.setCurrentMonthPrincipalIncreaseDecrease(
                    subtract(BigDecimal.ZERO, reduceAmount));
            newEntity.setCurrentMonthPrincipal(
                    subtract(newEntity.getCurrentMonthPrincipal(), reduceAmount));
            newEntity.setAnnualCumulativeRecovery(reduceAmount);
            newEntity.setCurrentMonthDisposedDebt(reduceAmount);

            nonLitigationClaimRepository.save(newEntity);
            existingOrNew = newEntity;
        }

        // 获取当前系统日期
        Calendar currentCal = Calendar.getInstance();
        int currentYear = currentCal.get(Calendar.YEAR);
        int currentMonth = currentCal.get(Calendar.MONTH) + 1; // Calendar月份从0开始

        // 获取处置记录的年月
        int recordYear = existingOrNew.getId().getYear();
        int recordMonth = existingOrNew.getId().getMonth();

        // 检查是否为过去月份的处置
        if (recordYear < currentYear || (recordYear == currentYear && recordMonth < currentMonth)) {
            logger.info("检测到在非当前月份({}-{})处置非诉讼债权，将更新后续月份数据", recordYear, recordMonth);

            // 从下一个月开始，一直更新到当前月份
            Calendar nextMonthCal = Calendar.getInstance();
            nextMonthCal.set(Calendar.YEAR, recordYear);
            nextMonthCal.set(Calendar.MONTH, recordMonth - 1); // Calendar月份从0开始
            nextMonthCal.add(Calendar.MONTH, 1);

            // 当前月份的期末本金，用于传递给后续月份
            BigDecimal currentMonthPrincipal = existingOrNew.getCurrentMonthPrincipal();
            NonLitigationClaim currentRecord = existingOrNew;

            // 循环直到当前月份
            while (nextMonthCal.get(Calendar.YEAR) < currentYear ||
                   (nextMonthCal.get(Calendar.YEAR) == currentYear &&
                    nextMonthCal.get(Calendar.MONTH) + 1 <= currentMonth)) {

                int nextYear = nextMonthCal.get(Calendar.YEAR);
                int nextMonth = nextMonthCal.get(Calendar.MONTH) + 1;

                logger.info("尝试更新后续月份非诉讼表: {}-{}", nextYear, nextMonth);

                // 创建下一个月的主键
                NonLitigationClaim.NonLitigationCompositeKey nextKey = new NonLitigationClaim.NonLitigationCompositeKey(
                        currentRecord.getId().getCreditor(),
                        currentRecord.getId().getDebtor(),
                        currentRecord.getId().getPeriod(),
                        nextYear,
                        nextMonth);

                // 查找下一个月的记录
                Optional<NonLitigationClaim> nextMonthRecord = nonLitigationClaimRepository.findById(nextKey);

                if (nextMonthRecord.isPresent()) {
                    NonLitigationClaim nextClaim = nextMonthRecord.get();

                    // 更新下一个月的上月末本金
                    nextClaim.setLastMonthPrincipal(currentMonthPrincipal);

                    // 重新计算下一个月的本月末本金
                    BigDecimal nextMonthPrincipalIncrease = nextClaim.getCurrentMonthPrincipalIncreaseDecrease() != null ?
                                                            nextClaim.getCurrentMonthPrincipalIncreaseDecrease() : BigDecimal.ZERO;
                    BigDecimal nextMonthPrincipal = currentMonthPrincipal.add(nextMonthPrincipalIncrease);

                    nextClaim.setCurrentMonthPrincipal(nextMonthPrincipal);

                    // 保存更新后的下一个月记录
                    nonLitigationClaimRepository.save(nextClaim);
                    logger.info("已更新后续月份({}-{})非诉讼数据: 上月末本金={}, 本月末本金={}",
                                nextYear, nextMonth, currentMonthPrincipal, nextMonthPrincipal);

                    // 更新当前记录和当前月末本金，用于下一次循环
                    currentRecord = nextClaim;
                    currentMonthPrincipal = nextMonthPrincipal;
                } else {
                    logger.info("未找到后续月份({}-{})的非诉讼记录，将创建新记录", nextYear, nextMonth);

                    // 创建新的非诉讼记录
                    NonLitigationClaim nextClaim = new NonLitigationClaim();
                    nextClaim.setId(nextKey);

                    // 复制基础信息
                    nextClaim.setManagementCompany(currentRecord.getManagementCompany());
                    nextClaim.setDueDate(currentRecord.getDueDate());
                    nextClaim.setAnnualRecoveryTarget(currentRecord.getAnnualRecoveryTarget());
                    nextClaim.setAnnualCumulativeRecovery(currentRecord.getAnnualCumulativeRecovery());

                    // 设置上月末本金
                    nextClaim.setLastMonthPrincipal(currentMonthPrincipal);

                    // 设置本月本金增减为0
                    nextClaim.setCurrentMonthPrincipalIncreaseDecrease(BigDecimal.ZERO);
                    nextClaim.setCurrentMonthDisposedDebt(BigDecimal.ZERO);

                    // 设置本月末本金等于上月末本金
                    nextClaim.setCurrentMonthPrincipal(currentMonthPrincipal);

                    // 保存新记录
                    nonLitigationClaimRepository.save(nextClaim);
                    logger.info("创建了后续月份({}-{})非诉讼数据: 上月末本金={}, 本月末本金={}",
                                nextYear, nextMonth, currentMonthPrincipal, currentMonthPrincipal);

                    // 更新当前记录，用于下一次循环
                    currentRecord = nextClaim;
                }

                // 移动到下一个月
                nextMonthCal.add(Calendar.MONTH, 1);
            }
        }
        return ResponseEntity.ok(existingOrNew);
    }

    /**
     * 将处置表数据同步更新到诉讼表
     * 更新诉讼表中的本月处置金额，并相应减少本月末债权余额
     * 如果处置的是过去月份的债权，会同步更新后续月份的上月末债权余额和本月末债权余额
     *
     * @param reductionData 前端传入的处置信息json
     */
    @Transactional
    public ResponseEntity<?> updateLitigationClaimTable(Map<String, Object> reductionData) {
        Map<String, Object> idMap = (Map<String, Object>) reductionData.get("id");
        idMap.remove("isLitigation");
        logger.info("识别为涉诉对象: {}", reductionData);

        // 1. 转换成实体
        LitigationClaim newEntity = MapConvertUtil.convert(reductionData, LitigationClaim.class);
        LitigationClaim.LitigationCompositeKey key = newEntity.getId();

        // 确保主键字段不为 null
        if (key.getCreditor() == null || key.getDebtor() == null || key.getPeriod() == null) {
            logger.error("诉讼表主键字段不完整: creditor={}, debtor={}, period={}",
                         key.getCreditor(), key.getDebtor(), key.getPeriod());
            return ResponseEntity.badRequest().body("诉讼表主键字段不完整");
        }
        // 打印key中的额信息
        logger.info("诉讼表主键字段完整: creditor={}, debtor={}, period={}",
                    key.getCreditor(), key.getDebtor(), key.getPeriod());
        // 2. 获取处置金额
        BigDecimal reduceAmount = getBigDecimal(reductionData.get("monthlyReduceAmount"));

        // 3. 查询数据库是否已有该主键的记录
        Optional<LitigationClaim> optional = litigationClaimRepository.findById(key);
        LitigationClaim existingOrNew;

        if (optional.isPresent()) {
            LitigationClaim existing = optional.get();

            // ✅ 本月处置债权 += monthlyReduceAmount
            existing.setCurrentMonthDisposalDebt(
                    sum(existing.getCurrentMonthDisposalDebt(), reduceAmount)
                                                );

            // ✅ 本年度累计回收 += monthlyReduceAmount
            existing.setAnnualCumulativeRecovery(
                    sum(existing.getAnnualCumulativeRecovery(), reduceAmount)
                                                );

            // ✅ 本月末债权余额 -= monthlyReduceAmount
            existing.setCurrentMonthDebtBalance(
                    subtract(existing.getCurrentMonthDebtBalance(), reduceAmount)
                                               );

            // ✅ 调整涉诉债权本金，保持利息不变
            if (existing.getLitigationPrincipal() != null) {
                // 保存原始利息值
                BigDecimal originalInterest = existing.getLitigationInterest();

                // 只过当利息存在时才偏向调整本金
                if (originalInterest != null) {
                    // 先减少本金
                    BigDecimal newPrincipal = subtract(existing.getLitigationPrincipal(), reduceAmount);

                    // 确保利息不变，即 新余额 = 新本金 + 原利息
                    BigDecimal expectedBalance = newPrincipal.add(originalInterest);

                    // 预期债权余额
                    BigDecimal newBalance = subtract(existing.getCurrentMonthDebtBalance(), reduceAmount);

                    // 如果两者有差异，调整本金而非利息保持利息不变
                    if (newBalance.compareTo(expectedBalance) != 0) {
                        // 重新计算本金 = 债权余额 - 利息
                        newPrincipal = newBalance.subtract(originalInterest);
                        // 保证本金非负
                        if (newPrincipal.compareTo(BigDecimal.ZERO) < 0) {
                            newPrincipal = BigDecimal.ZERO;
                        }
                        existing.setLitigationPrincipal(newPrincipal);
                    } else {
                        existing.setLitigationPrincipal(newPrincipal);
                    }
                } else {
                    // 如果没有利息数据，直接减少本金
                    existing.setLitigationPrincipal(
                            subtract(existing.getLitigationPrincipal(), reduceAmount)
                                                   );
                }
            }

            // 可选：同步其他字段（如备注、责任人等）
            if (newEntity.getRemark() != null) {
                existing.setRemark(newEntity.getRemark());
            }
            if (newEntity.getManagementCompany() != null) {
                existing.setManagementCompany(newEntity.getManagementCompany());
            }

            litigationClaimRepository.save(existing);
            existingOrNew = existing;

        } else {
            // 不存在记录，新建时直接设置各字段
            newEntity.setCurrentMonthDisposalDebt(reduceAmount);
            newEntity.setCurrentMonthDebtBalance(
                    subtract(newEntity.getCurrentMonthDebtBalance(), reduceAmount)
                                                );
            newEntity.setAnnualCumulativeRecovery(reduceAmount);

            // 调整涉诉债权本金，保持利息不变
            if (newEntity.getLitigationPrincipal() != null) {
                // 保存原始利息值
                BigDecimal originalInterest = newEntity.getLitigationInterest();

                // 当利息存在时才有必要调整
                if (originalInterest != null) {
                    // 新本金 = 新债权余额 - 利息
                    BigDecimal newBalance = subtract(newEntity.getCurrentMonthDebtBalance(), reduceAmount);
                    BigDecimal newPrincipal = newBalance.subtract(originalInterest);
                    // 保证本金非负
                    if (newPrincipal.compareTo(BigDecimal.ZERO) < 0) {
                        newPrincipal = BigDecimal.ZERO;
                    }
                    newEntity.setLitigationPrincipal(newPrincipal);
                } else {
                    // 没有利息数据，直接减少本金
                    newEntity.setLitigationPrincipal(
                            subtract(newEntity.getLitigationPrincipal(), reduceAmount)
                                                    );
                }
            }
            litigationClaimRepository.save(newEntity);
            existingOrNew = newEntity;
        }

        // 获取当前系统日期
        Calendar currentCal = Calendar.getInstance();
        int currentYear = currentCal.get(Calendar.YEAR);
        int currentMonth = currentCal.get(Calendar.MONTH) + 1; // Calendar月份从0开始

        // 获取处置记录的年月
        int recordYear = existingOrNew.getId().getYear();
        int recordMonth = existingOrNew.getId().getMonth();

        // 检查是否为过去月份的处置
        if (recordYear < currentYear || (recordYear == currentYear && recordMonth < currentMonth)) {
            logger.info("检测到在非当前月份({}-{})处置诉讼债权，将更新后续月份数据", recordYear, recordMonth);

            // 从下一个月开始，一直更新到当前月份
            Calendar nextMonthCal = Calendar.getInstance();
            nextMonthCal.set(Calendar.YEAR, recordYear);
            nextMonthCal.set(Calendar.MONTH, recordMonth - 1); // Calendar月份从0开始
            nextMonthCal.add(Calendar.MONTH, 1);

            // 当前月份的期末余额，用于传递给后续月份
            BigDecimal currentMonthEndBalance = existingOrNew.getCurrentMonthDebtBalance();
            LitigationClaim currentRecord = existingOrNew;

            // 循环直到当前月份
            while (nextMonthCal.get(Calendar.YEAR) < currentYear ||
                   (nextMonthCal.get(Calendar.YEAR) == currentYear &&
                    nextMonthCal.get(Calendar.MONTH) + 1 <= currentMonth)) {

                int nextYear = nextMonthCal.get(Calendar.YEAR);
                int nextMonth = nextMonthCal.get(Calendar.MONTH) + 1;

                logger.info("尝试更新后续月份诉讼表: {}-{}", nextYear, nextMonth);

                // 创建下一个月的主键
                LitigationClaim.LitigationCompositeKey nextKey = new LitigationClaim.LitigationCompositeKey();
                nextKey.setCreditor(currentRecord.getId().getCreditor());
                nextKey.setDebtor(currentRecord.getId().getDebtor());
                nextKey.setPeriod(currentRecord.getId().getPeriod());
                nextKey.setYear(nextYear);
                nextKey.setMonth(nextMonth);

                // 查找下一个月的记录
                Optional<LitigationClaim> nextMonthRecord = litigationClaimRepository.findById(nextKey);

                if (nextMonthRecord.isPresent()) {
                    LitigationClaim nextClaim = nextMonthRecord.get();

                    // 更新下一个月的上月末债权余额
                    nextClaim.setLastMonthDebtBalance(currentMonthEndBalance);

                    // 重新计算下一个月的本月末债权余额
                    BigDecimal nextMonthNewDebt = nextClaim.getCurrentMonthNewDebt() != null ?
                                                  nextClaim.getCurrentMonthNewDebt() : BigDecimal.ZERO;
                    BigDecimal nextMonthDisposal = nextClaim.getCurrentMonthDisposalDebt() != null ?
                                                   nextClaim.getCurrentMonthDisposalDebt() : BigDecimal.ZERO;
                    BigDecimal nextMonthBalance = currentMonthEndBalance.add(nextMonthNewDebt).subtract(nextMonthDisposal);
                    nextClaim.setCurrentMonthDebtBalance(nextMonthBalance);

                    // 当本月末债权余额变化时，只让涉诉债权本金变化，保持涉诉应收利息不变
                    if (nextClaim.getLitigationPrincipal() != null) {
                        // 更新涉诉债权本金
                        // 先保存原来的利息值
                        BigDecimal originalInterest = nextClaim.getLitigationInterest();

                        // 根据新的债权余额调整本金
                        // 保证 本金 + 原利息 = 新余额
                        if (originalInterest != null) {
                            // 新本金 = 新余额 - 原利息
                            BigDecimal newPrincipal = nextMonthBalance.subtract(originalInterest);
                            // 如果新本金小于0，则设置为0，保证本金不为负
                            if (newPrincipal.compareTo(BigDecimal.ZERO) < 0) {
                                newPrincipal = BigDecimal.ZERO;
                            }
                            nextClaim.setLitigationPrincipal(newPrincipal);
                        }
                    }

                    // 保存更新的记录
                    litigationClaimRepository.save(nextClaim);

                    // 更新当前记录引用和当前月末余额，用于下一次循环
                    currentRecord = nextClaim;
                    currentMonthEndBalance = nextMonthBalance;
                } else {
                    logger.info("未找到后续月份({}-{})的诉讼记录，将创建新记录", nextYear, nextMonth);

                    // 创建新的诉讼记录
                    LitigationClaim nextClaim = new LitigationClaim();
                    nextClaim.setId(nextKey);

                    // 复制基础信息
                    nextClaim.setManagementCompany(currentRecord.getManagementCompany());
                    nextClaim.setLitigationCase(currentRecord.getLitigationCase());
                    nextClaim.setClaimType(currentRecord.getClaimType());
                    nextClaim.setDueDate(currentRecord.getDueDate());
                    nextClaim.setOverdueYear(currentRecord.getOverdueYear());
                    nextClaim.setSequence(currentRecord.getSequence());
                    nextClaim.setAnnualRecoveryTarget(currentRecord.getAnnualRecoveryTarget());
                    nextClaim.setAnnualCumulativeRecovery(currentRecord.getAnnualCumulativeRecovery());

                    // 设置上月末债权余额
                    nextClaim.setLastMonthDebtBalance(currentMonthEndBalance);

                    // 设置本月新增债权和处置债权为0
                    nextClaim.setCurrentMonthNewDebt(BigDecimal.ZERO);
                    nextClaim.setCurrentMonthDisposalDebt(BigDecimal.ZERO);

                    // 设置本月末债权余额等于上月末债权余额
                    nextClaim.setCurrentMonthDebtBalance(currentMonthEndBalance);

                    // 如果原记录有涉诉本金和利息信息，则也复制
                    if (currentRecord.getLitigationPrincipal() != null) {
                        nextClaim.setLitigationPrincipal(currentRecord.getLitigationPrincipal());
                    }
                    if (currentRecord.getLitigationInterest() != null) {
                        nextClaim.setLitigationInterest(currentRecord.getLitigationInterest());
                    }

                    // 保存新记录
                    litigationClaimRepository.save(nextClaim);
                    logger.info("创建了后续月份({}-{})诉讼数据: 上月末债权余额={}, 本月末债权余额={}",
                                nextYear, nextMonth, currentMonthEndBalance, currentMonthEndBalance);

                    // 更新当前记录引用，用于下一次循环
                    currentRecord = nextClaim;
                }

                // 移动到下一个月
                nextMonthCal.add(Calendar.MONTH, 1);
            }
        }

        return ResponseEntity.ok(existingOrNew);
    }

    /**
     * 将处置表数据同步更新到减值准备表
     * 如果处置的是过去月份的债权，会同步更新后续月份的上月末余额和本月末余额
     *
     * @param reductionData 前端传入的处置信息json
     */
    @Transactional
    public void impairmentReserveTable(Map<String, Object> reductionData) {
        logger.info("【减值准备表】准备处理数据: {}", reductionData);

        // 先转成实体对象
        ImpairmentReserve incoming = MapConvertUtil.convert(reductionData, ImpairmentReserve.class);
        ImpairmentReserve.ImpairmentReserveKey key = incoming.getId();

        // 确保 isLitigation 字段不为 null
        if (key.getIsLitigation() == null) {
            key.setIsLitigation("否");
            logger.info("在 impairmentReserveTable 中设置默认的 isLitigation 值为 '否'");
        }

        // 获取处置金额（来自 OverdueDebtDecrease 的字段）
        BigDecimal reduceAmount = reductionData.get("monthlyReduceAmount") != null
                                  ? new BigDecimal(String.valueOf(reductionData.get("monthlyReduceAmount")))
                                  : BigDecimal.ZERO;

        // 查找是否已有记录
        Optional<ImpairmentReserve> optionalExisting = impairmentReserveRepository.findById(key);
        ImpairmentReserve existingOrNew;

        if (optionalExisting.isPresent()) {
            ImpairmentReserve existing = optionalExisting.get();

            // ✅ 本月处置债权累加
            existing.setCurrentMonthDisposeDebt(sum(existing.getCurrentMonthDisposeDebt(), reduceAmount));

            // ✅ 本月末债权余额减少
            existing.setCurrentMonthBalance(subtract(existing.getCurrentMonthBalance(), reduceAmount));

            // ✅ 本年度累计回收增加
            existing.setAnnualCumulativeRecovery(sum(existing.getAnnualCumulativeRecovery(), reduceAmount));

            // ✅ 其他字段覆盖式更新（有值就覆盖）
            if (incoming.getRemark() != null) {
                existing.setRemark(incoming.getRemark());
            }
            if (incoming.getManagementCompany() != null) {
                existing.setManagementCompany(incoming.getManagementCompany());
            }

            // ✅ 更新 updateTime（由 @PreUpdate 自动完成，也可以手动写）
            existing.setUpdateTime(LocalDateTime.now());

            impairmentReserveRepository.save(existing);
            existingOrNew = existing;

        } else {
            // ✅ 新增记录，直接设置字段
            incoming.setCurrentMonthDisposeDebt(reduceAmount);
            incoming.setAnnualCumulativeRecovery(reduceAmount);

            // 如果没提供 currentMonthBalance，需要初始化为 0 - reduceAmount（也可以不设，由外部传入）
            if (incoming.getCurrentMonthBalance() == null) {
                incoming.setCurrentMonthBalance(BigDecimal.ZERO.subtract(reduceAmount));
            } else {
                incoming.setCurrentMonthBalance(incoming.getCurrentMonthBalance().subtract(reduceAmount));
            }

            impairmentReserveRepository.save(incoming);
            existingOrNew = incoming;
        }

        // 获取当前系统日期
        Calendar currentCal = Calendar.getInstance();
        int currentYear = currentCal.get(Calendar.YEAR);
        int currentMonth = currentCal.get(Calendar.MONTH) + 1; // Calendar月份从0开始

        // 获取处置记录的年月
        int recordYear = existingOrNew.getId().getYear();
        int recordMonth = existingOrNew.getId().getMonth();

        // 检查是否为过去月份的处置
        if (recordYear < currentYear || (recordYear == currentYear && recordMonth < currentMonth)) {
            logger.info("检测到在非当前月份({}-{})处置债权，将更新后续月份数据", recordYear, recordMonth);

            // 从下一个月开始，一直更新到当前月份
            Calendar nextMonthCal = Calendar.getInstance();
            nextMonthCal.set(Calendar.YEAR, recordYear);
            nextMonthCal.set(Calendar.MONTH, recordMonth - 1); // Calendar月份从0开始
            nextMonthCal.add(Calendar.MONTH, 1);

            // 当前月份的期末余额，用于传递给后续月份
            BigDecimal currentMonthEndBalance = existingOrNew.getCurrentMonthBalance();
            ImpairmentReserve currentRecord = existingOrNew;

            // 循环直到当前月份
            while (nextMonthCal.get(Calendar.YEAR) < currentYear ||
                   (nextMonthCal.get(Calendar.YEAR) == currentYear &&
                    nextMonthCal.get(Calendar.MONTH) + 1 <= currentMonth)) {

                int nextYear = nextMonthCal.get(Calendar.YEAR);
                int nextMonth = nextMonthCal.get(Calendar.MONTH) + 1;

                logger.info("尝试更新后续月份: {}-{}", nextYear, nextMonth);

                // 创建下一个月的主键
                ImpairmentReserve.ImpairmentReserveKey nextKey = new ImpairmentReserve.ImpairmentReserveKey(
                        currentRecord.getId().getCreditor(),
                        currentRecord.getId().getDebtor(),
                        nextYear,
                        nextMonth,
                        currentRecord.getId().getIsLitigation());
                nextKey.setPeriod(currentRecord.getId().getPeriod());

                // 查找下一个月的记录
                Optional<ImpairmentReserve> nextMonthRecord = impairmentReserveRepository.findById(nextKey);

                if (nextMonthRecord.isPresent()) {
                    ImpairmentReserve nextReserve = nextMonthRecord.get();

                    // 更新下一个月的上月末余额
                    nextReserve.setPreviousMonthBalance(currentMonthEndBalance);

                    // 重新计算下一个月的本月末余额
                    BigDecimal nextMonthIncreaseDecrease = nextReserve.getCurrentMonthIncreaseDecrease() != null ?
                                                           nextReserve.getCurrentMonthIncreaseDecrease() : BigDecimal.ZERO;
                    BigDecimal nextMonthAmount = currentMonthEndBalance.add(nextMonthIncreaseDecrease);
                    nextReserve.setCurrentMonthAmount(nextMonthAmount);
                    nextReserve.setCurrentMonthBalance(nextMonthAmount);

                    // 保存更新后的下一个月记录
                    impairmentReserveRepository.save(nextReserve);
                    logger.info("已更新后续月份({}-{})减值准备数据: 上月末余额={}, 本月末余额={}",
                                nextYear, nextMonth, currentMonthEndBalance, nextMonthAmount);

                    // 更新当前月份引用，用于下一次循环
                    currentRecord = nextReserve;
                    currentMonthEndBalance = nextMonthAmount;
                } else {
                    logger.info("未找到后续月份({}-{})的减值准备记录，将创建新记录", nextYear, nextMonth);

                    // 创建新的减值准备记录
                    ImpairmentReserve nextReserve = new ImpairmentReserve();
                    nextReserve.setId(nextKey);

                    // 复制基础信息
                    nextReserve.setManagementCompany(currentRecord.getManagementCompany());
                    nextReserve.setRemark(currentRecord.getRemark());
                    nextReserve.setAnnualRecoveryTarget(currentRecord.getAnnualRecoveryTarget());
                    nextReserve.setAnnualCumulativeRecovery(currentRecord.getAnnualCumulativeRecovery());

                    // 设置上月末余额
                    nextReserve.setPreviousMonthBalance(currentMonthEndBalance);

                    // 初始化其他金额字段
                    nextReserve.setCurrentMonthIncreaseDecrease(BigDecimal.ZERO);
                    nextReserve.setCurrentMonthDisposeDebt(BigDecimal.ZERO);

                    // 设置本月余额等于上月末余额
                    nextReserve.setCurrentMonthAmount(currentMonthEndBalance);
                    nextReserve.setCurrentMonthBalance(currentMonthEndBalance);

                    // 设置创建时间
                    nextReserve.setCreateTime(LocalDateTime.now());
                    nextReserve.setUpdateTime(LocalDateTime.now());

                    // 保存新记录
                    impairmentReserveRepository.save(nextReserve);
                    logger.info("创建了后续月份({}-{})减值准备数据: 上月末余额={}, 本月末余额={}",
                                nextYear, nextMonth, currentMonthEndBalance, currentMonthEndBalance);

                    // 更新当前记录引用，用于下一次循环
                    currentRecord = nextReserve;
                    // 注意：不需要更新currentMonthEndBalance，因为新记录的本月末余额等于当前月末余额
                }

                // 移动到下一个月
                nextMonthCal.add(Calendar.MONTH, 1);
            }
        }
    }

    /**
     * 根据债权人和债务人名称查询多条债务记录
     * <p>
     * 该方法主要用于债务处置页面的债权人和债务人信息查询。
     * 直接从减值准备表中搜索相关信息，并过滤掉余额为零的记录。
     * 返回的数据包含债权人、债务人、管理公司、期间、是否涉诉、上月余额、
     * 本月新增债权、本月处置债权、本月末余额等信息。
     * </p>
     * <p>
     * 处理流程：
     * 1. 从减值准备表中查询符合条件的记录
     * 2. 过滤掉余额为零的记录（表示已经处置完毕的债权）
     * 3. 将查询结果转换为Map对象列表，便于前端处理
     * </p>
     *
     * @param creditor 债权人名称（精确匹配）
     * @param debtor   债务人名称（精确匹配）
     * @return 匹配的债务记录信息的List<Map>，包含基本信息和金额信息
     */
    public List<java.util.Map<String, Object>> findDebtorInfoByCreditorAndDebtor(String creditor, String debtor) {
        // 从减值准备表中查询符合条件的记录，并对输入参数进行去除空格处理
        List<ImpairmentReserve> records = impairmentReserveRepository.findByIdCreditorAndIdDebtor(creditor.trim(), debtor.trim());
        List<Map<String, Object>> resultList = new ArrayList<>();

        // 遍历查询结果，过滤并转换为前端所需的数据格式
        for (ImpairmentReserve record : records) {
            // 获取本月末余额，用于过滤已处置完毕的债权
            BigDecimal balance = record.getCurrentMonthBalance();

            // 过滤掉余额为零的记录（表示已经处置完毕的债权）
            if (balance != null && balance.compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }

            // 创建新的Map对象存储记录信息
            Map<String, Object> recordMap = new java.util.HashMap<>();

            // 添加基本信息字段
            recordMap.put("creditor", record.getId().getCreditor());
            recordMap.put("debtor", record.getId().getDebtor());
            recordMap.put("managementCompany", record.getManagementCompany());
            recordMap.put("period", record.getId().getPeriod());
            recordMap.put("isLitigation", record.getId().getIsLitigation());
            if ("是".equals(record.getId().getIsLitigation()) && record.getCaseName() != null) {
                recordMap.put("caseName", record.getCaseName());
            }
            recordMap.put("subjectName", record.getSubjectName());

            // 添加金额相关字段
            recordMap.put("lastMonthBalance", record.getLastMonthBalance());
            recordMap.put("currentMonthNewDebt", record.getCurrentMonthNewDebt());
            recordMap.put("currentMonthDisposeDebt", record.getCurrentMonthDisposeDebt());
            recordMap.put("currentMonthBalance", record.getCurrentMonthBalance());

            // 将记录添加到结果列表
            resultList.add(recordMap);
        }
        logger.info("查询到{}条债务记录", resultList.size());
        return resultList;
    }

    public List<java.util.Map<String, Object>> findDecreaseDebtorInfoByYear(int year) {
        List<DebtDecreaseDTO> records = debtDecreaseQueryRepository.findDebtDecreaseByYear(year);
        List<Map<String, Object>> resultList = new ArrayList<>();

        for (DebtDecreaseDTO record : records) {
            Map<String, Object> recordMap = new java.util.HashMap<>();

            recordMap.put("creditor", record.getCreditor());
            recordMap.put("debtor", record.getDebtor());
            recordMap.put("managementCompany", record.getManagementCompany());
            recordMap.put("period", record.getPeriod());
            recordMap.put("month", record.getMonth());
            recordMap.put("isLitigation", record.getIsLitigation());
            recordMap.put("currentMonthDisposeDebt", record.getCurrentMonthDisposeDebt());
            recordMap.put("cashDisposal", record.getCashDisposal());
            recordMap.put("installmentRepayment", record.getInstallmentRepayment());
            recordMap.put("assetDebt", record.getAssetDebt());
            recordMap.put("otherWays", record.getOtherWays());


            resultList.add(recordMap);
        }
        return resultList;
    }

    private void mergeEntity(OverdueDebtDecrease target, OverdueDebtDecrease source) {
        // 累加型字段
        target.setMonthlyReduceAmount(sum(target.getMonthlyReduceAmount(), source.getMonthlyReduceAmount()));
        target.setCashDisposal(sum(target.getCashDisposal(), source.getCashDisposal()));
        target.setInstallmentRepayment(sum(target.getInstallmentRepayment(), source.getInstallmentRepayment()));
        target.setAssetDebt(sum(target.getAssetDebt(), source.getAssetDebt()));
        target.setOtherWays(sum(target.getOtherWays(), source.getOtherWays()));
        target.setTotalReduceAmount(sum(target.getTotalReduceAmount(), source.getTotalReduceAmount()));

        // 有值覆盖型字段
        if (source.getManagementCompany() != null) {
            target.setManagementCompany(source.getManagementCompany());
        }
        if (source.getRemark() != null) {
            target.setRemark(source.getRemark());
        }

        // 更新时间无论如何更新
        target.setUpdateTime(LocalDateTime.now());
    }

    // 安全累加：防止 null + 数字 报错
    private BigDecimal sum(BigDecimal a, BigDecimal b) {
        return (a == null ? BigDecimal.ZERO : a).add(b == null ? BigDecimal.ZERO : b);
    }

    private BigDecimal subtract(BigDecimal a, BigDecimal b) {
        return (a == null ? BigDecimal.ZERO : a).subtract(b == null ? BigDecimal.ZERO : b);
    }

    private BigDecimal getBigDecimal(Object val) {
        if (val == null) return BigDecimal.ZERO;
        try {
            return new BigDecimal(val.toString());
        } catch (Exception e) {
            return BigDecimal.ZERO;
        }
    }

    /**
     * 真正删除处置记录
     * 物理删除处置表记录，并反向更新相关表数据
     *
     * @param deleteData 前端传入的删除数据
     * @return 处理结果
     */
    @Transactional
    public ResponseEntity<?> deleteDisposalRecord(Map<String, Object> deleteData) {
        logger.info("开始执行真正删除处置记录: {}", deleteData);

        try {
            // 1. 解析删除目标的主键信息
            OverdueDebtDecrease.OverdueDebtDecreaseKey key = parseDeleteKey(deleteData);
            if (key == null) {
                return ResponseEntity.badRequest().body("删除数据格式错误，无法解析主键信息");
            }

            // 2. 查找要删除的处置记录
            Optional<OverdueDebtDecrease> optionalRecord = overdueDebtDecreaseRepository.findById(key);
            if (!optionalRecord.isPresent()) {
                return ResponseEntity.badRequest().body("未找到要删除的处置记录");
            }

            OverdueDebtDecrease disposalRecord = optionalRecord.get();
            BigDecimal disposalAmount = disposalRecord.getMonthlyReduceAmount() != null ?
                disposalRecord.getMonthlyReduceAmount() : BigDecimal.ZERO;

            logger.info("找到要删除的处置记录: 债权人={}, 债务人={}, 处置金额={}",
                       disposalRecord.getCreditor(), disposalRecord.getDebtor(), disposalAmount);

            // 3. 物理删除处置表记录
            overdueDebtDecreaseRepository.delete(disposalRecord);
            logger.info("已物理删除处置表记录");

            // 4. 反向更新减值准备表
            reverseUpdateImpairmentReserve(disposalRecord, disposalAmount);

            // 5. 反向更新诉讼表或非诉讼表
            if ("是".equals(disposalRecord.getIsLitigation())) {
                reverseUpdateLitigationClaim(disposalRecord, disposalAmount);
            } else {
                reverseUpdateNonLitigationClaim(disposalRecord, disposalAmount);
            }

            // 6. 清理处置表中的空记录
            cleanupEmptyDisposalRecords(disposalRecord.getCreditor(), disposalRecord.getDebtor(),
                                      disposalRecord.getPeriod(), disposalRecord.getYear());

            logger.info("处置记录删除完成: 债权人={}, 债务人={}",
                       disposalRecord.getCreditor(), disposalRecord.getDebtor());

            return ResponseEntity.ok("删除成功");

        } catch (Exception e) {
            logger.error("删除处置记录时发生错误", e);
            return ResponseEntity.status(500).body("删除处置记录失败: " + e.getMessage());
        }
    }

    /**
     * 解析删除数据中的主键信息
     */
    private OverdueDebtDecrease.OverdueDebtDecreaseKey parseDeleteKey(Map<String, Object> deleteData) {
        try {
            String creditor = (String) deleteData.get("creditor");
            String debtor = (String) deleteData.get("debtor");
            String period = (String) deleteData.get("period");
            Integer year = (Integer) deleteData.get("year");
            Integer month = (Integer) deleteData.get("month");
            String isLitigation = (String) deleteData.get("isLitigation");

            if (creditor == null || debtor == null || period == null ||
                year == null || month == null || isLitigation == null) {
                logger.error("删除数据主键字段不完整: creditor={}, debtor={}, period={}, year={}, month={}, isLitigation={}",
                           creditor, debtor, period, year, month, isLitigation);
                return null;
            }

            OverdueDebtDecrease.OverdueDebtDecreaseKey key = new OverdueDebtDecrease.OverdueDebtDecreaseKey();
            key.setCreditor(creditor);
            key.setDebtor(debtor);
            key.setPeriod(period);
            key.setYear(year);
            key.setMonth(new BigDecimal(month));
            key.setIsLitigation(isLitigation);

            return key;

        } catch (Exception e) {
            logger.error("解析删除数据主键时发生错误", e);
            return null;
        }
    }

    /**
     * 反向更新减值准备表
     * 减少本月处置债权，增加本月末余额
     */
    private void reverseUpdateImpairmentReserve(OverdueDebtDecrease disposalRecord, BigDecimal disposalAmount) {
        logger.info("开始反向更新减值准备表: 减少处置债权={}", disposalAmount);
        
        try {
            // 构建减值准备表的主键
            ImpairmentReserve.ImpairmentReserveKey key = new ImpairmentReserve.ImpairmentReserveKey(
                disposalRecord.getCreditor(),
                disposalRecord.getDebtor(),
                disposalRecord.getYear(),
                disposalRecord.getMonth().intValue(),
                disposalRecord.getIsLitigation()
            );
            key.setPeriod(disposalRecord.getPeriod());
            
            // 查找减值准备记录
            Optional<ImpairmentReserve> optionalReserve = impairmentReserveRepository.findById(key);
            
            if (optionalReserve.isPresent()) {
                ImpairmentReserve reserve = optionalReserve.get();
                
                // 反向更新：减少本月处置债权
                BigDecimal currentDisposeDebt = reserve.getCurrentMonthDisposeDebt() != null ?
                    reserve.getCurrentMonthDisposeDebt() : BigDecimal.ZERO;
                BigDecimal newDisposeDebt = currentDisposeDebt.subtract(disposalAmount);
                
                // 确保不出现负数
                if (newDisposeDebt.compareTo(BigDecimal.ZERO) < 0) {
                    logger.warn("反向更新后本月处置债权为负数，设置为0: 原值={}, 减少={}", 
                        currentDisposeDebt, disposalAmount);
                    newDisposeDebt = BigDecimal.ZERO;
                }
                
                reserve.setCurrentMonthDisposeDebt(newDisposeDebt);
                
                // 反向更新：增加本月末债权余额
                reserve.setCurrentMonthBalance(sum(reserve.getCurrentMonthBalance(), disposalAmount));
                
                // 反向更新：减少本年度累计回收
                BigDecimal currentRecovery = reserve.getAnnualCumulativeRecovery() != null ?
                    reserve.getAnnualCumulativeRecovery() : BigDecimal.ZERO;
                BigDecimal newRecovery = currentRecovery.subtract(disposalAmount);
                
                // 确保累计回收不为负数
                if (newRecovery.compareTo(BigDecimal.ZERO) < 0) {
                    newRecovery = BigDecimal.ZERO;
                }
                
                reserve.setAnnualCumulativeRecovery(newRecovery);
                
                // 保存更新
                impairmentReserveRepository.save(reserve);
                
                logger.info("成功反向更新减值准备表: 债权人={}, 债务人={}, 新处置债权={}, 新余额={}", 
                    disposalRecord.getCreditor(), disposalRecord.getDebtor(),
                    newDisposeDebt, reserve.getCurrentMonthBalance());
                    
                // 如果删除的是过去月份的处置，需要更新后续月份的余额
                updateSubsequentMonthsAfterDeletion(reserve, disposalAmount);
                
            } else {
                logger.warn("未找到对应的减值准备记录，无法反向更新: 债权人={}, 债务人={}, 年={}, 月={}", 
                    disposalRecord.getCreditor(), disposalRecord.getDebtor(),
                    disposalRecord.getYear(), disposalRecord.getMonth());
            }
            
        } catch (Exception e) {
            logger.error("反向更新减值准备表时发生错误", e);
            throw new RuntimeException("反向更新减值准备表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 删除处置记录后更新后续月份的减值准备表
     * 
     * @param currentReserve 当前月份的减值准备记录
     * @param disposalAmount 被删除的处置金额
     */
    private void updateSubsequentMonthsAfterDeletion(ImpairmentReserve currentReserve, BigDecimal disposalAmount) {
        // 获取当前系统日期
        Calendar currentCal = Calendar.getInstance();
        int currentYear = currentCal.get(Calendar.YEAR);
        int currentMonth = currentCal.get(Calendar.MONTH) + 1;
        
        // 获取记录的年月
        int recordYear = currentReserve.getId().getYear();
        int recordMonth = currentReserve.getId().getMonth();
        
        // 如果是过去月份的处置，需要更新后续月份
        if (recordYear < currentYear || (recordYear == currentYear && recordMonth < currentMonth)) {
            logger.info("检测到删除过去月份({}-{})的处置记录，需要更新后续月份", recordYear, recordMonth);
            
            // 从下一个月开始更新
            Calendar nextMonthCal = Calendar.getInstance();
            nextMonthCal.set(Calendar.YEAR, recordYear);
            nextMonthCal.set(Calendar.MONTH, recordMonth - 1);
            nextMonthCal.add(Calendar.MONTH, 1);
            
            // 当前月份的期末余额（已经反向更新过了）
            BigDecimal currentMonthEndBalance = currentReserve.getCurrentMonthBalance();
            
            // 循环更新到当前月份
            while (nextMonthCal.get(Calendar.YEAR) < currentYear ||
                   (nextMonthCal.get(Calendar.YEAR) == currentYear &&
                    nextMonthCal.get(Calendar.MONTH) + 1 <= currentMonth)) {
                
                int nextYear = nextMonthCal.get(Calendar.YEAR);
                int nextMonth = nextMonthCal.get(Calendar.MONTH) + 1;
                
                // 创建下一个月的主键
                ImpairmentReserve.ImpairmentReserveKey nextKey = new ImpairmentReserve.ImpairmentReserveKey(
                    currentReserve.getId().getCreditor(),
                    currentReserve.getId().getDebtor(),
                    nextYear,
                    nextMonth,
                    currentReserve.getId().getIsLitigation()
                );
                nextKey.setPeriod(currentReserve.getId().getPeriod());
                
                // 查找并更新下一个月的记录
                Optional<ImpairmentReserve> nextMonthRecord = impairmentReserveRepository.findById(nextKey);
                
                if (nextMonthRecord.isPresent()) {
                    ImpairmentReserve nextReserve = nextMonthRecord.get();
                    
                    // 更新上月末余额
                    nextReserve.setPreviousMonthBalance(currentMonthEndBalance);
                    
                    // 增加本月末余额（因为删除了过去的处置）
                    nextReserve.setCurrentMonthBalance(sum(nextReserve.getCurrentMonthBalance(), disposalAmount));
                    nextReserve.setCurrentMonthAmount(sum(nextReserve.getCurrentMonthAmount(), disposalAmount));
                    
                    // 保存更新
                    impairmentReserveRepository.save(nextReserve);
                    
                    logger.info("已更新后续月份({}-{})减值准备表: 增加余额={}", 
                        nextYear, nextMonth, disposalAmount);
                    
                    // 更新当前月末余额，用于下一次循环
                    currentMonthEndBalance = nextReserve.getCurrentMonthBalance();
                }
                
                // 移动到下一个月
                nextMonthCal.add(Calendar.MONTH, 1);
            }
        }
    }
    
    /**
     * 反向更新诉讼表
     * 减少本月处置债权，增加本月末债权余额
     */
    private void reverseUpdateLitigationClaim(OverdueDebtDecrease disposalRecord, BigDecimal disposalAmount) {
        logger.info("开始反向更新诉讼表: 减少处置债权={}", disposalAmount);
        
        try {
            // 构建诉讼表的主键
            LitigationClaim.LitigationCompositeKey key = new LitigationClaim.LitigationCompositeKey();
            key.setCreditor(disposalRecord.getCreditor());
            key.setDebtor(disposalRecord.getDebtor());
            key.setPeriod(disposalRecord.getPeriod());
            key.setYear(disposalRecord.getYear());
            key.setMonth(disposalRecord.getMonth().intValue());
            
            // 查找诉讼记录
            Optional<LitigationClaim> optionalClaim = litigationClaimRepository.findById(key);
            
            if (optionalClaim.isPresent()) {
                LitigationClaim claim = optionalClaim.get();
                
                // 反向更新：减少本月处置债权
                BigDecimal currentDisposal = claim.getCurrentMonthDisposalDebt() != null ?
                    claim.getCurrentMonthDisposalDebt() : BigDecimal.ZERO;
                BigDecimal newDisposal = currentDisposal.subtract(disposalAmount);
                
                // 确保不出现负数
                if (newDisposal.compareTo(BigDecimal.ZERO) < 0) {
                    logger.warn("反向更新后本月处置债权为负数，设置为0: 原值={}, 减少={}", 
                        currentDisposal, disposalAmount);
                    newDisposal = BigDecimal.ZERO;
                }
                
                claim.setCurrentMonthDisposalDebt(newDisposal);
                
                // 反向更新：增加本月末债权余额
                claim.setCurrentMonthDebtBalance(sum(claim.getCurrentMonthDebtBalance(), disposalAmount));
                
                // 反向更新：减少本年度累计回收
                BigDecimal currentRecovery = claim.getAnnualCumulativeRecovery() != null ?
                    claim.getAnnualCumulativeRecovery() : BigDecimal.ZERO;
                BigDecimal newRecovery = currentRecovery.subtract(disposalAmount);
                
                if (newRecovery.compareTo(BigDecimal.ZERO) < 0) {
                    newRecovery = BigDecimal.ZERO;
                }
                
                claim.setAnnualCumulativeRecovery(newRecovery);
                
                // 反向调整涉诉债权本金（增加本金，保持利息不变）
                if (claim.getLitigationPrincipal() != null) {
                    claim.setLitigationPrincipal(sum(claim.getLitigationPrincipal(), disposalAmount));
                }
                
                // 保存更新
                litigationClaimRepository.save(claim);
                
                logger.info("成功反向更新诉讼表: 债权人={}, 债务人={}, 新处置债权={}, 新余额={}", 
                    disposalRecord.getCreditor(), disposalRecord.getDebtor(),
                    newDisposal, claim.getCurrentMonthDebtBalance());
                
            } else {
                logger.warn("未找到对应的诉讼记录，无法反向更新: 债权人={}, 债务人={}, 年={}, 月={}", 
                    disposalRecord.getCreditor(), disposalRecord.getDebtor(),
                    disposalRecord.getYear(), disposalRecord.getMonth());
            }
            
        } catch (Exception e) {
            logger.error("反向更新诉讼表时发生错误", e);
            throw new RuntimeException("反向更新诉讼表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 反向更新非诉讼表
     * 减少本月处置债权，增加本月末本金
     */
    private void reverseUpdateNonLitigationClaim(OverdueDebtDecrease disposalRecord, BigDecimal disposalAmount) {
        logger.info("开始反向更新非诉讼表: 减少处置债权={}", disposalAmount);
        
        try {
            // 构建非诉讼表的主键
            NonLitigationClaim.NonLitigationCompositeKey key = new NonLitigationClaim.NonLitigationCompositeKey(
                disposalRecord.getCreditor(),
                disposalRecord.getDebtor(),
                disposalRecord.getPeriod(),
                disposalRecord.getYear(),
                disposalRecord.getMonth().intValue()
            );
            
            // 查找非诉讼记录
            Optional<NonLitigationClaim> optionalClaim = nonLitigationClaimRepository.findById(key);
            
            if (optionalClaim.isPresent()) {
                NonLitigationClaim claim = optionalClaim.get();
                
                // 反向更新：减少本月处置债权
                BigDecimal currentDisposed = claim.getCurrentMonthDisposedDebt() != null ?
                    claim.getCurrentMonthDisposedDebt() : BigDecimal.ZERO;
                BigDecimal newDisposed = currentDisposed.subtract(disposalAmount);
                
                // 确保不出现负数
                if (newDisposed.compareTo(BigDecimal.ZERO) < 0) {
                    logger.warn("反向更新后本月处置债权为负数，设置为0: 原值={}, 减少={}", 
                        currentDisposed, disposalAmount);
                    newDisposed = BigDecimal.ZERO;
                }
                
                claim.setCurrentMonthDisposedDebt(newDisposed);
                
                // 反向更新：增加本月本金增减
                claim.setCurrentMonthPrincipalIncreaseDecrease(
                    sum(claim.getCurrentMonthPrincipalIncreaseDecrease(), disposalAmount));
                
                // 反向更新：增加本月末本金
                claim.setCurrentMonthPrincipal(sum(claim.getCurrentMonthPrincipal(), disposalAmount));
                
                // 反向更新：减少本年度累计回收
                BigDecimal currentRecovery = claim.getAnnualCumulativeRecovery() != null ?
                    claim.getAnnualCumulativeRecovery() : BigDecimal.ZERO;
                BigDecimal newRecovery = currentRecovery.subtract(disposalAmount);
                
                if (newRecovery.compareTo(BigDecimal.ZERO) < 0) {
                    newRecovery = BigDecimal.ZERO;
                }
                
                claim.setAnnualCumulativeRecovery(newRecovery);
                
                // 保存更新
                nonLitigationClaimRepository.save(claim);
                
                logger.info("成功反向更新非诉讼表: 债权人={}, 债务人={}, 新处置债权={}, 新本金={}", 
                    disposalRecord.getCreditor(), disposalRecord.getDebtor(),
                    newDisposed, claim.getCurrentMonthPrincipal());
                
            } else {
                logger.warn("未找到对应的非诉讼记录，无法反向更新: 债权人={}, 债务人={}, 年={}, 月={}", 
                    disposalRecord.getCreditor(), disposalRecord.getDebtor(),
                    disposalRecord.getYear(), disposalRecord.getMonth());
            }
            
        } catch (Exception e) {
            logger.error("反向更新非诉讼表时发生错误", e);
            throw new RuntimeException("反向更新非诉讼表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 清理处置表中的空记录
     * 删除金额为0且无实际意义的处置记录
     */
    private void cleanupEmptyDisposalRecords(String creditor, String debtor, String period, int year) {
        logger.info("开始清理空的处置记录: 债权人={}, 债务人={}, 期间={}, 年份={}", creditor, debtor, period, year);
        // 简化实现：这里可以添加具体的清理逻辑
        logger.info("空记录清理完成");
    }
}
