import React, { useEffect, useRef, useState } from 'react';
import Handsontable from 'handsontable';
import 'handsontable/dist/handsontable.full.min.css';
import * as XLSX from 'xlsx';
import { Box, CircularProgress, Alert } from '@mui/material';
import api from '../../../utils/api';
import { useAuth } from '../../../context/AuthContext';

/**
 * ExcelViewer Component - Personal Use Only
 *
 * This component uses Handsontable Community Edition under non-commercial license
 * for personal learning and evaluation purposes only.
 *
 * License Compliance Notice:
 * - This implementation is strictly for non-commercial use
 * - Not for company/enterprise usage
 * - Not for production deployment
 * - For learning and personal projects only
 */
// eslint-disable-next-line react/prop-types
const ExcelViewer = ({ fileUrl, currentSheet = 0, requiredRole = 'USER' }) => {
  const { user, isAuthenticated } = useAuth();
  const hotContainerRef = useRef(null);
  const hotInstanceRef = useRef(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const isMounted = useRef(true);

  // 检查用户权限
  const checkPermission = () => {
    if (!isAuthenticated) {
      throw new Error('请先登录');
    }

    const userRole = user?.role || 'USER';
    const hasPermission =
      userRole === 'ROLE_ADMIN' || userRole === requiredRole || userRole.includes(requiredRole);

    if (!hasPermission) {
      throw new Error('您没有权限访问此文件');
    }

    return true;
  };

  useEffect(() => {
    // 组件挂载时设置标志
    isMounted.current = true;

    // 组件卸载时清理
    return () => {
      isMounted.current = false;
      if (hotInstanceRef.current) {
        hotInstanceRef.current.destroy();
        hotInstanceRef.current = null;
      }
    };
  }, []);

  useEffect(() => {
    const LICENSE_TYPE = 'non-commercial-and-evaluation';

    const initializeTable = (data = [['加载中...']]) => {
      if (!hotContainerRef.current || !isMounted.current) {
        return null;
      }

      // 如果已存在实例，先销毁
      if (hotInstanceRef.current) {
        hotInstanceRef.current.destroy();
        hotInstanceRef.current = null;
      }

      const hot = new Handsontable(hotContainerRef.current, {
        data,
        rowHeaders: true,
        colHeaders: true,
        licenseKey: LICENSE_TYPE,
        stretchH: 'all',
        autoWrapRow: true,
        height: 'auto',
        maxRows: 100,
        language: 'zh-CN',
        contextMenu: false,
        readOnly: true,
        mergeCells: true,
        manualColumnResize: true,
        manualRowResize: true,
        allowInsertRow: false,
        allowInsertColumn: false,
        allowRemoveRow: false,
        allowRemoveColumn: false,
        comments: false,
      });

      return hot;
    };

    const loadExcelFile = async () => {
      if (!isMounted.current) {
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // 权限检查
        checkPermission();

        // 获取认证信息
        const token =
          localStorage.getItem('token') || JSON.parse(localStorage.getItem('auth') || '{}').token;

        if (!token) {
          throw new Error('未找到认证令牌，请重新登录');
        }

        // 添加调试信息
        console.log('准备下载文件:', {
          url: fileUrl,
          userRole: user?.role,
          requiredRole,
          hasToken: !!token,
          tokenPrefix: token ? token.substring(0, 15) + '...' : 'none',
        });

        const response = await api.get(fileUrl, {
          responseType: 'arraybuffer',
          headers: {
            Accept: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            Authorization: `Bearer ${token}`,
            'X-User-Role': user?.role || 'USER', // 添加角色信息到请求头
          },
        });

        if (!isMounted.current) {
          return;
        }

        // 检查响应类型
        const contentType = response.headers['content-type'];
        if (contentType?.includes('application/json')) {
          const decoder = new TextDecoder('utf-8');
          const jsonStr = decoder.decode(response.data);
          const errorData = JSON.parse(jsonStr);
          throw new Error(errorData.message || '服务器返回了错误响应');
        }

        // 解析 Excel 数据
        const workbook = XLSX.read(response.data, { type: 'array' });

        if (!workbook.SheetNames.length) {
          throw new Error('Excel 文件没有工作表');
        }

        const sheetName = workbook.SheetNames[currentSheet];
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet, {
          header: 1,
          defval: '',
          raw: false,
        });

        // 检查组件是否仍然挂载
        if (!isMounted.current) {
          return;
        }

        // 重新初始化表格并加载数据
        hotInstanceRef.current = initializeTable(jsonData);

        if (hotInstanceRef.current && isMounted.current) {
          const colWidths = Array(jsonData[0]?.length || 0).fill(120);

          hotInstanceRef.current.updateSettings({
            colWidths,
            cells: (row, col) => ({
              className: 'htCenter htMiddle',
              readOnly: true,
            }),
            afterGetColHeader: (col, TH) => {
              TH.style.background = '#f3f3f3';
              TH.style.fontWeight = 'bold';
              TH.style.textAlign = 'center';
            },
          });
        }
      } catch (err) {
        console.error('文件加载详细错误:', {
          message: err.message,
          status: err.response?.status,
          statusText: err.response?.statusText,
          userRole: user?.role,
          requiredRole,
          url: fileUrl,
        });

        if (isMounted.current) {
          const errorMessage = err.message || '加载文件失败';
          setError(errorMessage);
          hotInstanceRef.current = initializeTable([[`错误：${errorMessage}`]]);
        }
      } finally {
        if (isMounted.current) {
          setLoading(false);
        }
      }
    };

    loadExcelFile();
  }, [fileUrl, currentSheet]);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height={400}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box
      ref={hotContainerRef}
      sx={{
        width: '100%',
        height: 'calc(100vh - 300px)',
        overflow: 'auto',
        position: 'relative',
        '& .handsontable': {
          fontSize: '14px',
          fontFamily: '"Roboto","Helvetica","Arial",sans-serif',
        },
        '& .htCenter': {
          textAlign: 'center',
        },
        '& .htMiddle': {
          verticalAlign: 'middle',
        },
      }}
    />
  );
};

// 添加使用声明
ExcelViewer.LICENSE_NOTICE = `
This component uses Handsontable Community Edition under non-commercial license.
Version: 13.1.0
License Key: non-commercial-and-evaluation
Usage Restrictions:
- Personal learning and evaluation only
- Not for commercial use
- Not for production deployment
`;

export default ExcelViewer;
