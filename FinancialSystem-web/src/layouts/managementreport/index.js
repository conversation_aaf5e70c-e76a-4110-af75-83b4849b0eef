import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Button,
  Grid,
  IconButton,
  InputBase,
  Paper,
  Toolbar,
  Typography,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Tab,
  Tabs,
  Container,
  Divider,
} from '@mui/material';
import {
  Search as SearchIcon,
  Save as SaveIcon,
  Print as PrintIcon,
  Refresh as RefreshIcon,
  GetApp as DownloadIcon,
  Close as CloseIcon,
  MoreVert as MoreVertIcon,
} from '@mui/icons-material';
import DashboardLayout from 'examples/LayoutContainers/DashboardLayout';
import DashboardNavbar from 'examples/Navbars/DashboardNavbar';
import Footer from 'examples/Footer';
import ExcelViewer from './components/ExcelViewer';

const styles = {
  paper: {
    padding: 2,
    margin: '10px 0',
    borderRadius: 1,
    boxShadow: '0 2px 8px 0 rgba(0,0,0,0.1)',
    backgroundColor: '#ffffff',
  },
  title: {
    fontSize: '18px',
    fontWeight: 600,
    color: '#1a237e',
    marginBottom: 1.5,
    textAlign: 'center',
  },
  toolbarButtonStyle: {
    textTransform: 'none',
    mx: 0.5,
  },
};

function ManagementReport() {
  const [year, setYear] = useState(new Date().getFullYear().toString());
  const [month, setMonth] = useState((new Date().getMonth() + 1).toString().padStart(2, '0'));
  const [currentCity, setCurrentCity] = useState('万润科技');
  const [reportType, setReportType] = useState('月报');
  const [currentSheet, setCurrentSheet] = useState(0);
  const containerRef = useRef(null);

  // 处理工作表切换
  const handleSheetChange = (event, newValue) => {
    setCurrentSheet(newValue);
  };

  return (
    <DashboardLayout>
      <DashboardNavbar />
      <Container maxWidth="xl">
        <Box sx={{ mt: 2, mb: 3 }}>
          <Paper sx={styles.paper} elevation={0}>
            <Typography variant="h5" sx={styles.title}>
              管理报表
            </Typography>
            <Divider sx={{ mb: 2 }} />

            {/* 工具栏 */}
            <Paper sx={{ mb: 2, overflow: 'hidden' }}>
              <Toolbar
                sx={{
                  minHeight: '48px',
                  backgroundColor: '#f5f5f5',
                  display: 'flex',
                  justifyContent: 'space-between',
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Button startIcon={<SaveIcon />} sx={styles.toolbarButtonStyle}>
                    保存
                  </Button>
                  <Button startIcon={<RefreshIcon />} sx={styles.toolbarButtonStyle}>
                    编制检查
                  </Button>
                  <Button startIcon={<PrintIcon />} sx={styles.toolbarButtonStyle}>
                    打印
                  </Button>

                  <FormControl size="small" sx={{ ml: 1, minWidth: 100 }}>
                    <Select
                      value={year}
                      onChange={e => setYear(e.target.value)}
                      displayEmpty
                      variant="outlined"
                      size="small"
                      sx={{ backgroundColor: 'white', height: '32px' }}
                    >
                      <MenuItem value="2024">2024年</MenuItem>
                      <MenuItem value="2023">2023年</MenuItem>
                    </Select>
                  </FormControl>

                  <FormControl size="small" sx={{ ml: 1, minWidth: 100 }}>
                    <Select
                      value={reportType}
                      onChange={e => setReportType(e.target.value)}
                      displayEmpty
                      variant="outlined"
                      size="small"
                      sx={{ backgroundColor: 'white', height: '32px' }}
                    >
                      <MenuItem value="月报">月报</MenuItem>
                      <MenuItem value="季报">季报</MenuItem>
                      <MenuItem value="年报">年报</MenuItem>
                    </Select>
                  </FormControl>
                </Box>

                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <IconButton size="small" color="inherit">
                    <MoreVertIcon />
                  </IconButton>
                  <IconButton size="small" color="inherit">
                    <CloseIcon />
                  </IconButton>
                </Box>
              </Toolbar>

              {/* 其他工具栏内容保持不变 */}
            </Paper>

            {/* 表格内容区域 */}
            <Paper
              sx={{
                p: 2,
                border: '1px solid #ddd',
                borderRadius: '4px',
                boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                backgroundColor: '#fff',
                overflowX: 'auto',
              }}
              ref={containerRef}
            >
              <ExcelViewer fileUrl="/template/逾期债权期初数.xlsx" currentSheet={currentSheet} />
            </Paper>

            {/* 底部标记 */}
            <Box sx={{ mt: 3, mb: 1, display: 'flex', justifyContent: 'center' }}>
              <Typography variant="body2" color="text.secondary">
                合并资产负债表 - {year}年{month}月 - {currentCity}
              </Typography>
            </Box>
          </Paper>
        </Box>
      </Container>
    </DashboardLayout>
  );
}

export default ManagementReport;
