import React, { useState, useRef, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Box, Paper, Grid, Typography } from '@mui/material';

const FormSelect = ({ label, value, onChange, options, required, error, disabled }) => {
  // 使用state控制选项弹出框的显示
  const [showOptions, setShowOptions] = useState(false);

  // 创建ref来引用组件DOM元素
  const selectRef = useRef(null);

  // 找到当前选中项的标签文本
  const selectedOption = options.find(opt => opt.value === value);
  const selectedLabel = selectedOption ? selectedOption.label : '请选择';

  // 处理点击外部区域关闭选项框
  useEffect(() => {
    function handleClickOutside(event) {
      if (selectRef.current && !selectRef.current.contains(event.target)) {
        setShowOptions(false);
      }
    }

    // 添加事件监听器
    document.addEventListener('mousedown', handleClickOutside);

    // 清理函数
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 处理选项点击
  const handleOptionClick = optionValue => {
    // 如果组件被禁用，不处理点击
    if (disabled) {
      return;
    }

    // 创建一个模拟的事件对象，以便与原有的onChange处理兼容
    const mockEvent = {
      target: { value: optionValue },
    };
    onChange(mockEvent);
    setShowOptions(false);
  };

  return (
    <Box sx={{ mb: 1.5 }} ref={selectRef}>
      <Typography
        variant="body2"
        component="label"
        sx={{
          display: 'block',
          mb: 0.5,
          fontSize: '13px',
          fontWeight: 500,
          color: '#333',
        }}
      >
        {label} {required && <span style={{ color: '#d32f2f' }}>*</span>}
      </Typography>
      <div
        onClick={() => !disabled && setShowOptions(!showOptions)}
        onKeyPress={e => {
          if (!disabled && (e.key === 'Enter' || e.key === ' ')) {
            setShowOptions(!showOptions);
          }
        }}
        role="combobox"
        aria-expanded={showOptions}
        aria-haspopup="listbox"
        aria-disabled={disabled}
        tabIndex={disabled ? '-1' : '0'}
        style={{
          width: '100%',
          padding: '6px 10px',
          borderRadius: '4px',
          border: '1px solid #ccc',
          backgroundColor: disabled ? '#f5f5f5' : '#fff',
          cursor: disabled ? 'not-allowed' : 'pointer',
          height: '32px',
          display: 'flex',
          alignItems: 'center',
          position: 'relative',
          fontSize: '13px',
          color: disabled ? '#9e9e9e' : 'inherit',
          opacity: disabled ? 0.7 : 1,
        }}
      >
        {selectedLabel}
      </div>

      {showOptions && !disabled && (
        <Paper
          elevation={2}
          style={{
            position: 'absolute',
            width: 'calc(100% - 20px)',
            zIndex: 1000,
            marginTop: '2px',
            maxHeight: '180px',
            overflowY: 'auto',
          }}
        >
          <Grid container spacing={0} role="listbox">
            {options.map((option, index) => (
              <Grid item xs={12} key={`option-${option.value}`}>
                <Box
                  sx={{
                    padding: '6px 10px',
                    borderBottom: index < options.length - 1 ? '1px solid #eee' : 'none',
                    cursor: 'pointer',
                    fontSize: '13px',
                    height: '32px',
                    display: 'flex',
                    alignItems: 'center',
                    '&:hover': { backgroundColor: '#f5f5f5' },
                    backgroundColor: option.value === value ? '#e3f2fd' : 'transparent',
                  }}
                  onClick={() => handleOptionClick(option.value)}
                >
                  {option.label}
                </Box>
              </Grid>
            ))}
          </Grid>
        </Paper>
      )}
      {error && (
        <Typography variant="caption" sx={{ color: '#d32f2f', display: 'block', mt: 0.5 }}>
          {error}
        </Typography>
      )}
    </Box>
  );
};

// 添加 PropTypes 校验
FormSelect.propTypes = {
  label: PropTypes.string.isRequired,
  value: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
  options: PropTypes.array.isRequired,
  required: PropTypes.bool,
  error: PropTypes.string,
  disabled: PropTypes.bool,
};

// 设置默认值
FormSelect.defaultProps = {
  disabled: false,
  required: false,
};

export default FormSelect;

// 这是隐藏的原始select元素，用于表单提交，但视觉上使用自定义UI
// <select
//   value={value}
//   onChange={onChange}
//   required={required}
//   style={{ display: 'none' }}
// >
//   <option value="">请选择</option>
//   {options.map((option, index) => (
//     <option key={index} value={option.value}>
//       {option.label}
//     </option>
//   ))}
// </select>
