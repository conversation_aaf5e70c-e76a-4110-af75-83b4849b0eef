spring:
  # 多数据源配置
  datasource:
    # 主数据源配置
    url: ${DB_PRIMARY_URL:****************************************************************************************************************}
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:Zlb&198838}
    driver-class-name: com.mysql.cj.jdbc.Driver

    primary:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: ${DB_PRIMARY_URL:****************************************************************************************************************&autoReconnect=true&useSSL=false&autoCommit=false}
      username: ${DB_USERNAME:root}
      password: ${DB_PASSWORD:Zlb&198838}
      hikari:
        maximum-pool-size: 20
        minimum-idle: 5
        connection-timeout: 30000
        idle-timeout: 600000
        max-lifetime: 1800000
        auto-commit: false  # 关键：强制禁用自动提交
        connection-init-sql: "SET autocommit=0"  # 连接初始化时强制设置

    # 第二数据源配置（金蝶数据库）
    secondary:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: ${DB_SECONDARY_URL:*************************************************************************************************************************}
      username: ${DB_USERNAME:root}
      password: ${DB_PASSWORD:Zlb&198838}
      hikari:
        maximum-pool-size: 10
        minimum-idle: 2
        connection-timeout: 30000
        idle-timeout: 600000
        max-lifetime: 1800000
        auto-commit: false  # 关键：强制禁用自动提交

    # 第三数据源配置（用户系统数据库）
    user-system:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: ${DB_USER_SYSTEM_URL:***********************************************************************************************************************************************************}
      username: ${DB_USERNAME:root}
      password: ${DB_PASSWORD:Zlb&198838}
      hikari:
        maximum-pool-size: 10
        minimum-idle: 2
        connection-timeout: 30000
        idle-timeout: 600000
        max-lifetime: 1800000
        auto-commit: false
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    # 禁用JPA事务管理
    open-in-view: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect
        format_sql: true
        packagesToScan: com.laoshu198838.entity
        physical_naming_strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
        # 完全禁用缓存
        cache:
          use_second_level_cache: false
          use_query_cache: false
        # 禁用事务相关功能
        connection:
          autocommit: false
        current_session_context_class: thread
  main:
    allow-bean-definition-overriding: true
  # 禁用Spring Cache
  cache:
    type: none
  # 禁用JCache自动配置
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration
      - org.springframework.boot.autoconfigure.cache.JCacheAutoConfiguration

  # Flyway数据库版本管理配置
  flyway:
    enabled: true
    baseline-on-migrate: true  # 关键：允许在非空数据库上进行基线迁移
    baseline-version: 1
    baseline-description: "Initial baseline"
    locations: classpath:db/migration
    sql-migration-prefix: V
    sql-migration-separator: __
    sql-migration-suffixes: .sql
    validate-on-migrate: true
    clean-disabled: true  # 生产环境安全设置

# JWT Configuration
jwt:
  secret: ${JWT_SECRET:financial-system-jwt-secret-key-2025-must-be-at-least-256-bits-long-for-hs512-algorithm-security}
  expiration: 86400000

# 注意：这些配置已经合并到上面的spring配置块中
# Flyway数据库版本管理配置 - 已合并
# Redis缓存配置 - 已合并
# 缓存配置 - 已合并

# Server Configuration
server:
  port: 8080

# Actuator监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,env,configprops,beans,mappings,caches,threaddump,heapdump
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
      show-components: always
    metrics:
      enabled: true
    prometheus:
      enabled: true
  metrics:
    distribution:
      percentiles-histogram:
        "[http.server.requests]": true
      percentiles:
        "[http.server.requests]": 0.5, 0.95, 0.99
    tags:
      application: financial-system
  prometheus:
    metrics:
      export:
        enabled: true

# SQL日志配置
logging:
  level:
    # 关闭SQL详细日志显示
    org.hibernate.SQL: INFO
    org.hibernate.type.descriptor.sql.BasicBinder: INFO

## 检查配置
#checks:
#  checks:
#    - key: newAmount
#      label: "新增金额"
#      tables:
#        新增表: "本月新增金额"
#        诉讼表: "本月新增债权"
#        非诉讼表: "本月新增债权"
#        减值准备表: "本月新增债权"
#
#    - key: disposedAmount
#      label: "处置金额"
#      tables:
#        新增表: "本月处置金额"
#        诉讼表: "本月处置债权"
#        非诉讼表: "本月处置债权"
#        减值准备表: "本月处置债权"
#
#    - key: endingBalance
#      label: "期末余额"
#      tables:
#        新增表: "债权余额"
#        诉讼表: "本月末债权余额"
#        非诉讼表: "本月末本金"
#        减值准备表: "本月末债权余额"

# 司库系统配置
treasury:
  # 中信银行司库系统接口配置
  endpoint: http://**********:6789
  username: ***********
  client-code: SZWR003_ZL
  client-name: 深圳万润科技股份有限公司ERP

  # 账户配置
  default-account: 8110701012901269085

  # 连接配置
  connection:
    timeout: 30000  # 连接超时时间(毫秒)
    read-timeout: 60000  # 读取超时时间(毫秒)
    retry-count: 3  # 重试次数

  # 缓存配置
  cache:
    enabled: true
    balance-cache-duration: 300  # 余额缓存时间(秒)
    transaction-cache-duration: 600  # 交易记录缓存时间(秒)

# 自定义应用配置
app:
  # 数据库备份路径 - 使用相对路径，相对于项目根目录
  backup:
    path: ../var/backups
  # 跨域配置
  cors:
    allowed-origins: http://localhost:3000,http://localhost:5173,http://**********:3000
