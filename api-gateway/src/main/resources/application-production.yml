spring:
  datasource:
    primary:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: ${SPRING_DATASOURCE_PRIMARY_URL}
      username: root
      password: ${DB_PASSWORD}
      hikari:
        maximum-pool-size: 20
        minimum-idle: 5
        connection-timeout: 30000
        idle-timeout: 600000
        max-lifetime: 1800000
        auto-commit: false

    secondary:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: ${SPRING_DATASOURCE_SECONDARY_URL}
      username: root
      password: ${DB_PASSWORD}
      hikari:
        maximum-pool-size: 10
        minimum-idle: 2
        auto-commit: false

    user-system:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: ${SPRING_DATASOURCE_USER_SYSTEM_URL}
      username: root
      password: ${DB_PASSWORD}
      hikari:
        maximum-pool-size: 10
        minimum-idle: 2
        auto-commit: false

  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false  # 生产环境关闭SQL日志

server:
  port: 8080
  address: 0.0.0.0

logging:
  level:
    org.hibernate.SQL: WARN
    org.hibernate.type.descriptor.sql.BasicBinder: WARN
    com.laoshu198838: INFO
