-- 数据库性能优化脚本
-- 执行时间: 2025-07-03
-- 作者: AI Assistant
-- 目标: 提升查询性能30-50%

USE `逾期债权数据库`;

-- ==========================================
-- 1. 新增表索引优化
-- ==========================================

-- 检查并创建管理公司索引
SET @index_exists_mgmt_company = (
    SELECT COUNT(*) FROM information_schema.STATISTICS 
    WHERE TABLE_SCHEMA = '逾期债权数据库' 
    AND TABLE_NAME = '新增表' 
    AND INDEX_NAME = 'idx_management_company'
);

SET @sql = IF(@index_exists_mgmt_company = 0, 
    'CREATE INDEX idx_management_company ON `新增表`(`管理公司`) COMMENT "管理公司索引"',
    'SELECT "idx_management_company索引已存在" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并创建月份索引
SET @index_exists_month = (
    SELECT COUNT(*) FROM information_schema.STATISTICS 
    WHERE TABLE_SCHEMA = '逾期债权数据库' 
    AND TABLE_NAME = '新增表' 
    AND INDEX_NAME = 'idx_month'
);

SET @sql = IF(@index_exists_month = 0, 
    'CREATE INDEX idx_month ON `新增表`(`月份`) COMMENT "月份索引"',
    'SELECT "idx_month索引已存在" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并创建债权性质索引
SET @index_exists_debt_nature = (
    SELECT COUNT(*) FROM information_schema.STATISTICS 
    WHERE TABLE_SCHEMA = '逾期债权数据库' 
    AND TABLE_NAME = '新增表' 
    AND INDEX_NAME = 'idx_debt_nature'
);

SET @sql = IF(@index_exists_debt_nature = 0, 
    'CREATE INDEX idx_debt_nature ON `新增表`(`债权性质`) COMMENT "债权性质索引"',
    'SELECT "idx_debt_nature索引已存在" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并创建金额索引
SET @index_exists_amount = (
    SELECT COUNT(*) FROM information_schema.STATISTICS 
    WHERE TABLE_SCHEMA = '逾期债权数据库' 
    AND TABLE_NAME = '新增表' 
    AND INDEX_NAME = 'idx_amount'
);

SET @sql = IF(@index_exists_amount = 0, 
    'CREATE INDEX idx_amount ON `新增表`(`金额`) COMMENT "金额索引"',
    'SELECT "idx_amount索引已存在" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并创建复合索引：公司+月份
SET @index_exists_company_month = (
    SELECT COUNT(*) FROM information_schema.STATISTICS 
    WHERE TABLE_SCHEMA = '逾期债权数据库' 
    AND TABLE_NAME = '新增表' 
    AND INDEX_NAME = 'idx_company_month'
);

SET @sql = IF(@index_exists_company_month = 0, 
    'CREATE INDEX idx_company_month ON `新增表`(`管理公司`, `月份`) COMMENT "公司月份复合索引"',
    'SELECT "idx_company_month索引已存在" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并创建复合索引：性质+金额
SET @index_exists_nature_amount = (
    SELECT COUNT(*) FROM information_schema.STATISTICS 
    WHERE TABLE_SCHEMA = '逾期债权数据库' 
    AND TABLE_NAME = '新增表' 
    AND INDEX_NAME = 'idx_nature_amount'
);

SET @sql = IF(@index_exists_nature_amount = 0, 
    'CREATE INDEX idx_nature_amount ON `新增表`(`债权性质`, `金额`) COMMENT "性质金额复合索引"',
    'SELECT "idx_nature_amount索引已存在" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并创建三字段复合索引：月份+公司+金额
SET @index_exists_month_company_amount = (
    SELECT COUNT(*) FROM information_schema.STATISTICS 
    WHERE TABLE_SCHEMA = '逾期债权数据库' 
    AND TABLE_NAME = '新增表' 
    AND INDEX_NAME = 'idx_month_company_amount'
);

SET @sql = IF(@index_exists_month_company_amount = 0, 
    'CREATE INDEX idx_month_company_amount ON `新增表`(`月份`, `管理公司`, `金额`) COMMENT "月份公司金额复合索引"',
    'SELECT "idx_month_company_amount索引已存在" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ==========================================
-- 2. 处置表索引优化
-- ==========================================

-- 检查处置表是否存在
SET @table_exists_disposal = (
    SELECT COUNT(*) FROM information_schema.TABLES 
    WHERE TABLE_SCHEMA = '逾期债权数据库' 
    AND TABLE_NAME = '处置表'
);

-- 如果处置表存在，则创建索引
SET @sql = IF(@table_exists_disposal > 0, 
    'SELECT "处置表存在，开始创建索引" as message',
    'SELECT "处置表不存在，跳过索引创建" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 处置表债权性质索引
SET @disposal_index1 = IF(@table_exists_disposal > 0, 
    CONCAT('CREATE INDEX IF NOT EXISTS idx_disposal_debt_nature ON `处置表`(`债权性质`) COMMENT "处置表债权性质索引"'),
    'SELECT "跳过处置表索引创建" as message'
);
SET @sql = @disposal_index1;
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 处置表处置方式索引
SET @disposal_index2 = IF(@table_exists_disposal > 0, 
    CONCAT('CREATE INDEX IF NOT EXISTS idx_disposal_method ON `处置表`(`处置方式`) COMMENT "处置方式索引"'),
    'SELECT "跳过处置表索引创建" as message'
);
SET @sql = @disposal_index2;
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ==========================================
-- 3. 用户系统数据库索引优化
-- ==========================================

USE `user_system`;

-- 公司表索引优化
-- 检查并创建公司名称索引
SET @company_name_index = (
    SELECT COUNT(*) FROM information_schema.STATISTICS 
    WHERE TABLE_SCHEMA = 'user_system' 
    AND TABLE_NAME = 'companies' 
    AND INDEX_NAME = 'idx_company_name'
);

SET @sql = IF(@company_name_index = 0, 
    'CREATE INDEX idx_company_name ON companies(company_name) COMMENT "公司名称索引"',
    'SELECT "idx_company_name索引已存在" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并创建公司简称索引
SET @company_short_name_index = (
    SELECT COUNT(*) FROM information_schema.STATISTICS 
    WHERE TABLE_SCHEMA = 'user_system' 
    AND TABLE_NAME = 'companies' 
    AND INDEX_NAME = 'idx_company_short_name'
);

SET @sql = IF(@company_short_name_index = 0, 
    'CREATE INDEX idx_company_short_name ON companies(company_short_name) COMMENT "公司简称索引"',
    'SELECT "idx_company_short_name索引已存在" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并创建层级上级复合索引
SET @level_parent_index = (
    SELECT COUNT(*) FROM information_schema.STATISTICS 
    WHERE TABLE_SCHEMA = 'user_system' 
    AND TABLE_NAME = 'companies' 
    AND INDEX_NAME = 'idx_level_parent'
);

SET @sql = IF(@level_parent_index = 0, 
    'CREATE INDEX idx_level_parent ON companies(level, parent_company_id) COMMENT "层级上级复合索引"',
    'SELECT "idx_level_parent索引已存在" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 用户表索引优化
-- 检查用户表是否存在
SET @users_table_exists = (
    SELECT COUNT(*) FROM information_schema.TABLES 
    WHERE TABLE_SCHEMA = 'user_system' 
    AND TABLE_NAME = 'users'
);

-- 用户名索引
SET @username_index = IF(@users_table_exists > 0, 
    CONCAT('CREATE INDEX IF NOT EXISTS idx_username ON users(username) COMMENT "用户名索引"'),
    'SELECT "用户表不存在" as message'
);
SET @sql = @username_index;
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 公司ID索引
SET @user_company_index = IF(@users_table_exists > 0, 
    CONCAT('CREATE INDEX IF NOT EXISTS idx_user_company_id ON users(company_id) COMMENT "用户公司ID索引"'),
    'SELECT "用户表不存在" as message'
);
SET @sql = @user_company_index;
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ==========================================
-- 4. 索引使用率分析查询
-- ==========================================

-- 查看新创建的索引
SELECT 
    TABLE_SCHEMA,
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    SEQ_IN_INDEX,
    CARDINALITY,
    INDEX_COMMENT
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA IN ('逾期债权数据库', 'user_system')
    AND INDEX_NAME LIKE 'idx_%'
ORDER BY TABLE_SCHEMA, TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;

-- 显示优化完成信息
SELECT 
    '数据库索引优化完成' as status,
    NOW() as completion_time,
    '预期查询性能提升30-50%' as expected_improvement;

-- 建议的性能测试查询
SELECT '建议执行以下查询进行性能测试：' as suggestion;
SELECT '1. SELECT 管理公司, SUM(金额) FROM 新增表 WHERE 月份 = "2024-12" GROUP BY 管理公司;' as test_query_1;
SELECT '2. SELECT * FROM companies WHERE company_name LIKE "%万润%";' as test_query_2;
SELECT '3. SELECT COUNT(*) FROM 新增表 WHERE 债权性质 = "贸易债权";' as test_query_3;

COMMIT;