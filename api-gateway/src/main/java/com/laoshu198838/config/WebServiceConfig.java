package com.laoshu198838.config;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;

/**
 * Web服务配置类
 * 负责扫描所有业务模块的组件，配置核心Web服务Bean
 * 
 * 功能特性：
 * - 组件扫描：自动扫描所有业务模块的Spring组件
 * - JDBC配置：提供JdbcTemplate Bean用于数据库操作
 * - 模块化支持：支持多模块Maven项目的组件发现
 * 
 * 使用情况：
 * - Spring容器启动时自动执行组件扫描
 * - 数据访问层：使用jdbcTemplate进行数据库操作
 * - 所有业务模块：通过@ComponentScan自动注册Bean
 * 
 * 扫描范围：
 * - controller: 所有REST控制器
 * - service: 所有业务服务（包括各模块服务）
 * - config: 所有配置类
 * - filter: 所有过滤器
 * - util: 所有工具类
 * 
 * 注意：如果重构controller目录结构，此配置会自动适应
 * 
 * <AUTHOR>
 */
@Configuration
@ComponentScan(basePackages = {
    "com.laoshu198838.controller",           // 控制器层
    "com.laoshu198838.service",              // 服务层（包括所有业务模块）
    "com.laoshu198838.config",               // 配置类
    "com.laoshu198838.filter",               // 过滤器
    "com.laoshu198838.util"                  // 工具类
})
public class WebServiceConfig {

    /**
     * 配置JdbcTemplate Bean
     */
    @Bean
    public JdbcTemplate jdbcTemplate(@Qualifier("primaryDataSource") DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }
}
