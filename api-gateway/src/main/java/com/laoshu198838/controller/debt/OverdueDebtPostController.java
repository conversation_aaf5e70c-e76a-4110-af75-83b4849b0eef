package com.laoshu198838.controller;


import com.laoshu198838.entity.overdue_debt.OverdueDebtAdd;
import com.laoshu198838.model.overduedebt.dto.entity.OverdueDebtAddDTO;
import com.laoshu198838.service.DebtManagementService;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/debts")
@CrossOrigin(origins = {"http://localhost:3000", "http://**********:3000"}) // 允许跨域
public class OverdueDebtPostController {

    private static final Logger logger = LoggerFactory.getLogger(OverdueDebtPostController.class);

    private final DebtManagementService debtManagementService;

    public OverdueDebtPostController(DebtManagementService debtManagementService) {
        this.debtManagementService = debtManagementService;
    }

    @PostMapping("/add")
    public OverdueDebtAdd addOverdueDebt(@Valid @RequestBody OverdueDebtAddDTO dto) {
        logger.info("=================== add前端传入的债务处置数据 ===================");
        logger.info("接收到前端传入的债务处置数据: {}", dto);
        logger.info("=======================================================");
        return debtManagementService.addOverdueDebt(dto);
    }

    /**
     * 处理逾期债权处置更新数据
     *
     * @param reductionData 前端提交的处置数据
     * @return 处理结果
     */
    @PostMapping("/update/reduction")
    public ResponseEntity<?> updateReduction(@RequestBody Map<String, Object> reductionData) {
        try {
            // 记录详细的前端数据
            logger.info("=================== 前端传入的债务处置数据 ===================");
            logger.info("接收到前端传入的债务处置数据: {}", reductionData);
            
            // 验证必要字段
            if (reductionData == null || reductionData.isEmpty()) {
                logger.error("请求数据为空");
                return ResponseEntity.badRequest().body("请求数据不能为空");
            }
            
            // 记录关键字段
            logger.info("债权人: {}", reductionData.get("creditor"));
            logger.info("债务人: {}", reductionData.get("debtor"));
            logger.info("是否涉诉: {}", reductionData.get("isLitigation"));
            logger.info("期间: {}", reductionData.get("period"));
            logger.info("年月: {}", reductionData.get("yearMonth"));
            logger.info("处置金额: {}", reductionData.get("dispositionAmount"));
            logger.info("处置明细: {}", reductionData.get("dispositionDetails"));
            logger.info("=======================================================");

            // 调用服务类方法将债权处置数据更新到数据库（包括处置表、减值准备表、诉讼表和非诉讼表）
            return debtManagementService.updateDebtReductionData(reductionData);
            
        } catch (Exception e) {
            logger.error("处理债权处置更新时发生错误", e);
            logger.error("错误类型: {}", e.getClass().getName());
            logger.error("错误消息: {}", e.getMessage());
            logger.error("错误堆栈: ", e);
            
            // 返回更详细的错误信息
            String errorMessage = String.format("处理失败: %s - %s", 
                e.getClass().getSimpleName(), 
                e.getMessage() != null ? e.getMessage() : "未知错误");
            
            return ResponseEntity.status(500).body(errorMessage);
        }
    }

    /**
     * 真正删除处置记录的API
     * 与负数累加不同，这个API会完全删除处置记录并清理相关数据
     *
     * @param deleteData 前端提交的删除数据
     * @return 处理结果
     */
    @DeleteMapping("/delete/disposal")
    public ResponseEntity<?> deleteDisposalRecord(@RequestBody Map<String, Object> deleteData) {
        logger.info("=================== 前端传入的删除处置数据 ===================");
        logger.info("接收到前端传入的删除处置数据: {}", deleteData);
        logger.info("=======================================================");

        try {
            return debtManagementService.deleteDisposalRecord(deleteData);
        } catch (Exception e) {
            logger.error("删除处置记录时发生错误", e);
            return ResponseEntity.status(500)
                    .body("删除处置记录时发生错误: " + e.getMessage());
        }
    }
}