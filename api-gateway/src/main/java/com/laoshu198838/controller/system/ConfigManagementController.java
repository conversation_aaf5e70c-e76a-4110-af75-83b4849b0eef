package com.laoshu198838.controller;

import com.laoshu198838.service.ConfigManagementService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 配置管理控制器
 * 提供系统配置管理相关的API接口
 * 
 * <AUTHOR>
 */
@Tag(name = "配置管理", description = "系统配置管理相关API")
@RestController
@RequestMapping("/api/admin/config")
@CrossOrigin(origins = {"${app.cors.allowed-origins:http://localhost:3000,http://localhost:5173}"}, allowCredentials = "true")
public class ConfigManagementController {
    
    @Autowired
    private ConfigManagementService configManagementService;
    
    /**
     * 获取所有系统配置
     */
    @Operation(summary = "获取所有系统配置", description = "获取系统中所有的配置项")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "403", description = "权限不足"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/all")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> getAllConfigs() {
        try {
            List<Map<String, Object>> configs = configManagementService.getAllConfigs();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("configs", configs);
            response.put("total", configs.size());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(error);
        }
    }
    
    /**
     * 获取分组配置
     */
    @Operation(summary = "获取分组配置", description = "按分组获取系统配置")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "403", description = "权限不足"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/groups")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> getConfigsByGroup() {
        try {
            Map<String, List<Map<String, Object>>> groupedConfigs = configManagementService.getConfigsByGroup();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("groups", groupedConfigs);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(error);
        }
    }
    
    /**
     * 获取指定配置值
     */
    @Operation(summary = "获取指定配置值", description = "根据配置键获取配置值")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "400", description = "参数无效"),
            @ApiResponse(responseCode = "403", description = "权限不足"),
            @ApiResponse(responseCode = "404", description = "配置不存在"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/{key}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> getConfigValue(
            @Parameter(description = "配置键", required = true)
            @PathVariable String key) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            String value = configManagementService.getConfigValue(key);
            
            if (value != null) {
                response.put("success", true);
                response.put("key", key);
                response.put("value", value);
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "配置不存在");
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 设置配置值
     */
    @Operation(summary = "设置配置值", description = "设置或更新系统配置")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "设置成功"),
            @ApiResponse(responseCode = "400", description = "参数无效"),
            @ApiResponse(responseCode = "403", description = "权限不足"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/{key}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> setConfigValue(
            @Parameter(description = "配置键", required = true)
            @PathVariable String key,
            @RequestBody Map<String, String> request) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            String value = request.get("value");
            String description = request.getOrDefault("description", "");
            String configType = request.getOrDefault("type", "STRING");
            
            if (value == null) {
                response.put("success", false);
                response.put("error", "配置值不能为空");
                return ResponseEntity.badRequest().body(response);
            }
            
            boolean success = configManagementService.setConfigValue(key, value, description, configType);
            
            if (success) {
                response.put("success", true);
                response.put("message", "配置设置成功");
                response.put("key", key);
                response.put("value", value);
            } else {
                response.put("success", false);
                response.put("message", "配置设置失败");
            }
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 删除配置
     */
    @Operation(summary = "删除配置", description = "删除指定的系统配置")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "删除成功"),
            @ApiResponse(responseCode = "400", description = "参数无效"),
            @ApiResponse(responseCode = "403", description = "权限不足"),
            @ApiResponse(responseCode = "404", description = "配置不存在"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @DeleteMapping("/{key}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> deleteConfig(
            @Parameter(description = "配置键", required = true)
            @PathVariable String key) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            boolean success = configManagementService.deleteConfig(key);
            
            if (success) {
                response.put("success", true);
                response.put("message", "配置删除成功");
                response.put("key", key);
            } else {
                response.put("success", false);
                response.put("message", "配置不存在或删除失败");
            }
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 批量设置配置
     */
    @Operation(summary = "批量设置配置", description = "批量设置多个系统配置")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "批量设置完成"),
            @ApiResponse(responseCode = "400", description = "参数无效"),
            @ApiResponse(responseCode = "403", description = "权限不足"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/batch")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> batchSetConfigs(
            @RequestBody Map<String, Map<String, String>> request) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            if (request == null || request.isEmpty()) {
                response.put("success", false);
                response.put("error", "请求数据不能为空");
                return ResponseEntity.badRequest().body(response);
            }
            
            Map<String, Boolean> results = configManagementService.batchSetConfigs(request);
            
            long successCount = results.values().stream().mapToLong(b -> b ? 1 : 0).sum();
            long failureCount = results.size() - successCount;
            
            response.put("success", true);
            response.put("results", results);
            response.put("total", results.size());
            response.put("successCount", successCount);
            response.put("failureCount", failureCount);
            response.put("message", String.format("批量设置完成：成功 %d 个，失败 %d 个", successCount, failureCount));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 重新加载配置
     */
    @Operation(summary = "重新加载配置", description = "重新加载所有配置到运行时环境")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "重新加载成功"),
            @ApiResponse(responseCode = "403", description = "权限不足"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/reload")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> reloadConfigs() {
        try {
            configManagementService.reloadAllConfigs();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "配置重新加载成功");
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(error);
        }
    }
    
    /**
     * 获取配置统计信息
     */
    @Operation(summary = "获取配置统计信息", description = "获取系统配置的统计信息")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "403", description = "权限不足"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/statistics")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> getConfigStatistics() {
        try {
            Map<String, Object> statistics = configManagementService.getConfigStatistics();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.putAll(statistics);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(error);
        }
    }
}
