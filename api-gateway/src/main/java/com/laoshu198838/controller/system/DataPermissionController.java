package com.laoshu198838.controller;

import com.laoshu198838.entity.user_system.Company;
import com.laoshu198838.entity.user_system.UserCompanyPermission;
import com.laoshu198838.service.DataPermissionService;
import com.laoshu198838.security.CustomUserDetails;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据权限管理控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/data-permissions")
@Tag(name = "数据权限管理", description = "用户公司数据访问权限管理")
public class DataPermissionController {

    private static final Logger logger = LoggerFactory.getLogger(DataPermissionController.class);

    @Autowired
    private DataPermissionService dataPermissionService;

    /**
     * 获取当前用户可访问的公司列表
     */
    @GetMapping("/accessible-companies")
    @Operation(summary = "获取用户可访问的公司列表")
    public ResponseEntity<Map<String, Object>> getAccessibleCompanies(Authentication authentication) {
        try {
            CustomUserDetails userDetails = (CustomUserDetails) authentication.getPrincipal();
            Long userId = userDetails.getId();

            List<Company> companies = dataPermissionService.getUserAccessibleCompanies(userId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", companies);
            response.put("message", "获取可访问公司列表成功");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取用户可访问公司列表失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取可访问公司列表失败：" + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 检查用户是否有访问指定公司数据的权限
     */
    @GetMapping("/check-access")
    @Operation(summary = "检查公司数据访问权限")
    public ResponseEntity<Map<String, Object>> checkCompanyAccess(
            @Parameter(description = "公司名称") @RequestParam String companyName,
            Authentication authentication) {
        try {
            CustomUserDetails userDetails = (CustomUserDetails) authentication.getPrincipal();
            Long userId = userDetails.getId();

            boolean hasAccess = dataPermissionService.hasCompanyAccess(userId, companyName);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("hasAccess", hasAccess);
            response.put("companyName", companyName);
            response.put("message", hasAccess ? "有权限访问" : "无权限访问");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("检查公司数据访问权限失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("hasAccess", false);
            response.put("message", "检查权限失败：" + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 获取用户的权限详情
     */
    @GetMapping("/user-permissions")
    @Operation(summary = "获取用户权限详情")
    public ResponseEntity<Map<String, Object>> getUserPermissions(
            @Parameter(description = "用户ID，为空则查询当前用户") @RequestParam(required = false) Long userId,
            Authentication authentication) {
        try {
            CustomUserDetails userDetails = (CustomUserDetails) authentication.getPrincipal();
            Long currentUserId = userDetails.getId();
            
            // 如果没有指定用户ID，则查询当前用户
            if (userId == null) {
                userId = currentUserId;
            }
            
            // 只有管理员才能查询其他用户的权限
            if (!userId.equals(currentUserId) && !dataPermissionService.isManagementAdmin(currentUserId)) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "无权限查看其他用户的权限信息");
                return ResponseEntity.status(403).body(response);
            }

            List<UserCompanyPermission> permissions = dataPermissionService.getUserPermissions(userId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", permissions);
            response.put("message", "获取用户权限详情成功");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取用户权限详情失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取用户权限详情失败：" + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 为用户授权公司数据访问权限（仅管理员）
     */
    @PostMapping("/grant")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "授权用户公司数据访问权限")
    public ResponseEntity<Map<String, Object>> grantCompanyAccess(
            @RequestBody GrantPermissionRequest request,
            Authentication authentication) {
        try {
            CustomUserDetails userDetails = (CustomUserDetails) authentication.getPrincipal();
            Long granterId = userDetails.getId();

            // 检查是否为万润科技管理员
            if (!dataPermissionService.isManagementAdmin(granterId)) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "只有万润科技的管理员才能授权数据访问权限");
                return ResponseEntity.status(403).body(response);
            }

            UserCompanyPermission permission = dataPermissionService.grantCompanyAccess(
                    request.getUserId(),
                    request.getCompanyId(),
                    request.getPermissionType(),
                    granterId,
                    request.getExpiresAt(),
                    request.getRemarks()
            );
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", permission);
            response.put("message", "权限授权成功");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("授权公司数据访问权限失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "授权失败：" + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 撤销用户公司数据访问权限（仅管理员）
     */
    @PostMapping("/revoke")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "撤销用户公司数据访问权限")
    public ResponseEntity<Map<String, Object>> revokeCompanyAccess(
            @RequestBody RevokePermissionRequest request,
            Authentication authentication) {
        try {
            CustomUserDetails userDetails = (CustomUserDetails) authentication.getPrincipal();
            Long currentUserId = userDetails.getId();

            // 检查是否为万润科技管理员
            if (!dataPermissionService.isManagementAdmin(currentUserId)) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "只有万润科技的管理员才能撤销数据访问权限");
                return ResponseEntity.status(403).body(response);
            }

            boolean success = dataPermissionService.revokeCompanyAccess(request.getUserId(), request.getCompanyId());
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", success);
            response.put("message", success ? "权限撤销成功" : "权限撤销失败");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("撤销公司数据访问权限失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "撤销失败：" + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 获取公司的用户权限列表（仅管理员）
     */
    @GetMapping("/company-users")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "获取公司的用户权限列表")
    public ResponseEntity<Map<String, Object>> getCompanyUserPermissions(
            @Parameter(description = "公司ID") @RequestParam Long companyId,
            Authentication authentication) {
        try {
            CustomUserDetails userDetails = (CustomUserDetails) authentication.getPrincipal();
            Long currentUserId = userDetails.getId();

            // 检查是否为万润科技管理员
            if (!dataPermissionService.isManagementAdmin(currentUserId)) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "只有万润科技的管理员才能查看公司用户权限列表");
                return ResponseEntity.status(403).body(response);
            }

            List<UserCompanyPermission> permissions = dataPermissionService.getCompanyUserPermissions(companyId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", permissions);
            response.put("message", "获取公司用户权限列表成功");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取公司用户权限列表失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取公司用户权限列表失败：" + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 获取即将过期的权限列表（仅管理员）
     */
    @GetMapping("/expiring")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "获取即将过期的权限列表")
    public ResponseEntity<Map<String, Object>> getExpiringPermissions(Authentication authentication) {
        try {
            CustomUserDetails userDetails = (CustomUserDetails) authentication.getPrincipal();
            Long currentUserId = userDetails.getId();

            // 检查是否为万润科技管理员
            if (!dataPermissionService.isManagementAdmin(currentUserId)) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "只有万润科技的管理员才能查看即将过期的权限");
                return ResponseEntity.status(403).body(response);
            }

            List<UserCompanyPermission> permissions = dataPermissionService.getExpiringPermissions();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", permissions);
            response.put("count", permissions.size());
            response.put("message", "获取即将过期权限列表成功");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取即将过期权限列表失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取即将过期权限列表失败：" + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 手动过期权限（仅管理员）
     */
    @PostMapping("/expire")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "手动过期权限")
    public ResponseEntity<Map<String, Object>> expirePermissions(Authentication authentication) {
        try {
            CustomUserDetails userDetails = (CustomUserDetails) authentication.getPrincipal();
            Long currentUserId = userDetails.getId();

            // 检查是否为万润科技管理员
            if (!dataPermissionService.isManagementAdmin(currentUserId)) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "只有万润科技的管理员才能执行权限过期操作");
                return ResponseEntity.status(403).body(response);
            }

            int expiredCount = dataPermissionService.expirePermissions();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("expiredCount", expiredCount);
            response.put("message", "成功过期 " + expiredCount + " 个权限记录");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("手动过期权限失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "过期权限失败：" + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    // 内部类：请求参数对象
    public static class GrantPermissionRequest {
        private Long userId;
        private Long companyId;
        private UserCompanyPermission.PermissionType permissionType;
        @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
        private LocalDateTime expiresAt;
        private String remarks;

        // Getters and Setters
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        
        public Long getCompanyId() { return companyId; }
        public void setCompanyId(Long companyId) { this.companyId = companyId; }
        
        public UserCompanyPermission.PermissionType getPermissionType() { return permissionType; }
        public void setPermissionType(UserCompanyPermission.PermissionType permissionType) { this.permissionType = permissionType; }
        
        public LocalDateTime getExpiresAt() { return expiresAt; }
        public void setExpiresAt(LocalDateTime expiresAt) { this.expiresAt = expiresAt; }
        
        public String getRemarks() { return remarks; }
        public void setRemarks(String remarks) { this.remarks = remarks; }
    }

    public static class RevokePermissionRequest {
        private Long userId;
        private Long companyId;

        // Getters and Setters
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        
        public Long getCompanyId() { return companyId; }
        public void setCompanyId(Long companyId) { this.companyId = companyId; }
    }
}