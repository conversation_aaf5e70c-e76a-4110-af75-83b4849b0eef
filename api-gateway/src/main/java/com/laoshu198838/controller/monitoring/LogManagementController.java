package com.laoshu198838.controller;

import com.laoshu198838.service.LogManagementService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 日志管理控制器
 * 提供日志管理相关的API接口
 * 
 * <AUTHOR>
 */
@Tag(name = "日志管理", description = "日志管理相关API")
@RestController
@RequestMapping("/api/admin/logs")
@CrossOrigin(origins = {"${app.cors.allowed-origins:http://localhost:3000,http://localhost:5173}"}, allowCredentials = "true")
public class LogManagementController {
    
    @Autowired
    private LogManagementService logManagementService;
    
    /**
     * 获取所有日志器信息
     */
    @Operation(summary = "获取所有日志器信息", description = "获取系统中所有日志器的配置信息")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "403", description = "权限不足"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/loggers")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> getAllLoggers() {
        try {
            List<Map<String, Object>> loggers = logManagementService.getAllLoggers();
            List<String> availableLevels = logManagementService.getAvailableLogLevels();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("loggers", loggers);
            response.put("availableLevels", availableLevels);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(error);
        }
    }
    
    /**
     * 设置日志器级别
     */
    @Operation(summary = "设置日志器级别", description = "动态设置指定日志器的日志级别")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "设置成功"),
            @ApiResponse(responseCode = "400", description = "参数无效"),
            @ApiResponse(responseCode = "403", description = "权限不足"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/loggers/{loggerName}/level")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> setLoggerLevel(
            @Parameter(description = "日志器名称", required = true)
            @PathVariable String loggerName,
            @Parameter(description = "日志级别", required = true)
            @RequestParam String level) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            boolean success = logManagementService.setLoggerLevel(loggerName, level);
            
            if (success) {
                response.put("success", true);
                response.put("message", "日志器 '" + loggerName + "' 级别已设置为 '" + level + "'");
            } else {
                response.put("success", false);
                response.put("message", "设置日志器级别失败");
            }
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 重置日志器级别
     */
    @Operation(summary = "重置日志器级别", description = "重置指定日志器的级别为继承父级别")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "重置成功"),
            @ApiResponse(responseCode = "400", description = "参数无效"),
            @ApiResponse(responseCode = "403", description = "权限不足"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @DeleteMapping("/loggers/{loggerName}/level")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> resetLoggerLevel(
            @Parameter(description = "日志器名称", required = true)
            @PathVariable String loggerName) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            boolean success = logManagementService.resetLoggerLevel(loggerName);
            
            if (success) {
                response.put("success", true);
                response.put("message", "日志器 '" + loggerName + "' 级别已重置");
            } else {
                response.put("success", false);
                response.put("message", "重置日志器级别失败");
            }
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取日志文件列表
     */
    @Operation(summary = "获取日志文件列表", description = "获取系统中所有日志文件的信息")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "403", description = "权限不足"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/files")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> getLogFiles() {
        try {
            List<Map<String, Object>> logFiles = logManagementService.getLogFiles();
            Map<String, Object> statistics = logManagementService.getLogStatistics();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("files", logFiles);
            response.put("statistics", statistics);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(error);
        }
    }
    
    /**
     * 读取日志文件内容
     */
    @Operation(summary = "读取日志文件内容", description = "读取指定日志文件的最后N行内容")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "读取成功"),
            @ApiResponse(responseCode = "400", description = "参数无效"),
            @ApiResponse(responseCode = "403", description = "权限不足"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/files/{fileName}/content")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> readLogFile(
            @Parameter(description = "日志文件名", required = true)
            @PathVariable String fileName,
            @Parameter(description = "读取行数", required = false)
            @RequestParam(defaultValue = "100") int lines) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            if (lines <= 0 || lines > 10000) {
                response.put("success", false);
                response.put("error", "行数必须在1-10000之间");
                return ResponseEntity.badRequest().body(response);
            }
            
            List<String> content = logManagementService.readLogFile(fileName, lines);
            
            response.put("success", true);
            response.put("fileName", fileName);
            response.put("lines", lines);
            response.put("content", content);
            response.put("actualLines", content.size());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 删除日志文件
     */
    @Operation(summary = "删除日志文件", description = "删除指定的日志文件")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "删除成功"),
            @ApiResponse(responseCode = "400", description = "参数无效"),
            @ApiResponse(responseCode = "403", description = "权限不足"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @DeleteMapping("/files/{fileName}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> deleteLogFile(
            @Parameter(description = "日志文件名", required = true)
            @PathVariable String fileName) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            boolean success = logManagementService.deleteLogFile(fileName);
            
            if (success) {
                response.put("success", true);
                response.put("message", "日志文件 '" + fileName + "' 已删除");
            } else {
                response.put("success", false);
                response.put("message", "日志文件不存在或删除失败");
            }
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 清理旧日志文件
     */
    @Operation(summary = "清理旧日志文件", description = "清理指定天数之前的旧日志文件")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "清理成功"),
            @ApiResponse(responseCode = "400", description = "参数无效"),
            @ApiResponse(responseCode = "403", description = "权限不足"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/cleanup")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> cleanupOldLogs(
            @Parameter(description = "保留天数", required = false)
            @RequestParam(defaultValue = "30") int daysToKeep) {
        
        try {
            if (daysToKeep < 1 || daysToKeep > 365) {
                Map<String, Object> error = new HashMap<>();
                error.put("success", false);
                error.put("error", "保留天数必须在1-365之间");
                return ResponseEntity.badRequest().body(error);
            }
            
            Map<String, Object> result = logManagementService.cleanOldLogFiles(daysToKeep);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(error);
        }
    }
    
    /**
     * 获取日志统计信息
     */
    @Operation(summary = "获取日志统计信息", description = "获取日志文件的统计信息")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "403", description = "权限不足"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/statistics")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> getLogStatistics() {
        try {
            Map<String, Object> statistics = logManagementService.getLogStatistics();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.putAll(statistics);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(error);
        }
    }
}
