package com.laoshu198838.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentSkipListSet;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.doAnswer;

/**
 * 测试环境Redis配置类，使用内存模拟实现
 * 
 * <AUTHOR>
 */
@Configuration
@Profile("test")
public class EmbeddedRedisConfig {
    
    private static final Logger logger = LoggerFactory.getLogger(EmbeddedRedisConfig.class);
    
    // 内存存储模拟 Redis
    private static final Map<String, Object> redisStore = new ConcurrentHashMap<>();
    private static final Set<String> redisKeys = new ConcurrentSkipListSet<>();
    
    
    
    /**
     * 模拟Redis连接工厂
     */
    @Bean
    @Primary
    public RedisConnectionFactory redisConnectionFactory() {
        logger.info("使用模拟Redis连接工厂进行测试");
        return mock(RedisConnectionFactory.class);
    }
    
    /**
     * 模拟RedisTemplate
     */
    @Bean
    @Primary
    @SuppressWarnings("unchecked")
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
        logger.info("使用模拟RedisTemplate进行测试");
        
        RedisTemplate<String, Object> template = mock(RedisTemplate.class);
        
        // 模拟 opsForValue 操作
        var valueOps = mock(org.springframework.data.redis.core.ValueOperations.class);
        when(template.opsForValue()).thenReturn(valueOps);
        
        // 模拟 set 操作
        doAnswer(invocation -> {
            String key = invocation.getArgument(0);
            Object value = invocation.getArgument(1);
            redisStore.put(key, value);
            redisKeys.add(key);
            logger.debug("模拟Redis: set {} = {}", key, value);
            return null;
        }).when(valueOps).set(anyString(), any());
        
        // 模拟 setex 操作 (带过期时间)
        doAnswer(invocation -> {
            String key = invocation.getArgument(0);
            Object value = invocation.getArgument(1);
            long timeout = invocation.getArgument(2);
            TimeUnit unit = invocation.getArgument(3);
            redisStore.put(key, value);
            redisKeys.add(key);
            logger.debug("模拟Redis: setex {} = {} (timeout: {} {})", key, value, timeout, unit);
            return null;
        }).when(valueOps).set(anyString(), any(), any(Long.class), any(TimeUnit.class));
        
        // 模拟 get 操作
        when(valueOps.get(anyString())).thenAnswer(invocation -> {
            String key = invocation.getArgument(0);
            Object value = redisStore.get(key);
            logger.debug("模拟Redis: get {} = {}", key, value);
            return value;
        });
        
        // 模拟 delete 操作
        when(template.delete(anyString())).thenAnswer(invocation -> {
            String key = invocation.getArgument(0);
            boolean existed = redisStore.containsKey(key);
            redisStore.remove(key);
            redisKeys.remove(key);
            logger.debug("模拟Redis: delete {} = {}", key, existed);
            return existed;
        });
        
        // 模拟 hasKey 操作
        when(template.hasKey(anyString())).thenAnswer(invocation -> {
            String key = invocation.getArgument(0);
            boolean exists = redisStore.containsKey(key);
            logger.debug("模拟Redis: hasKey {} = {}", key, exists);
            return exists;
        });
        
        // 模拟 keys 操作
        when(template.keys(anyString())).thenAnswer(invocation -> {
            String pattern = invocation.getArgument(0);
            Set<String> matchingKeys = new ConcurrentSkipListSet<>();
            for (String key : redisKeys) {
                if (pattern.equals("*") || key.contains(pattern.replace("*", ""))) {
                    matchingKeys.add(key);
                }
            }
            logger.debug("模拟Redis: keys {} = {}", pattern, matchingKeys);
            return matchingKeys;
        });
        
        // 模拟 expire 操作
        when(template.expire(anyString(), any(Long.class), any(TimeUnit.class))).thenAnswer(invocation -> {
            String key = invocation.getArgument(0);
            long timeout = invocation.getArgument(1);
            TimeUnit unit = invocation.getArgument(2);
            boolean exists = redisStore.containsKey(key);
            logger.debug("模拟Redis: expire {} {} {} = {}", key, timeout, unit, exists);
            return exists;
        });
        
        return template;
    }
}
