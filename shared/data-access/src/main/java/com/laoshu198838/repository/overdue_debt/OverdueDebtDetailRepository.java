package com.laoshu198838.repository.overdue_debt;

import com.laoshu198838.annotation.DataSource;
import com.laoshu198838.entity.overdue_debt.OverdueDebtSummary;
import com.laoshu198838.entity.overdue_debt.OverdueDebtSummary.OverdueDebtSummaryKey;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 逾期债权明细查询数据访问接口
 * 处理复杂的债权明细查询
 * <AUTHOR>
 */
@Repository
@DataSource("primary")
public interface OverdueDebtDetailRepository extends JpaRepository<OverdueDebtSummary, OverdueDebtSummaryKey> {

    //    根据传入的参数查询处置金额明细表
    @Query(value = """
                   # noinspection SqlAggregatesForFile @ column/"累计处置金额"
                               SELECT *
                               FROM (
                                   SELECT
                                       管理公司,
                                       债权人,
                                       债务人,
                                       期间,
                                       是否涉诉,
                                       SUM(CASE
                                           WHEN CAST(SUBSTRING(:month, 1, LENGTH(:month) - 1) AS SIGNED) = 12 AND 月份 BETWEEN 1 AND 12 THEN IFNULL(每月处置金额, 0)
                                           WHEN CAST(SUBSTRING(:month, 1, LENGTH(:month) - 1) AS SIGNED) = 11 AND 月份 BETWEEN 1 AND 11 THEN IFNULL(每月处置金额, 0)
                                           WHEN CAST(SUBSTRING(:month, 1, LENGTH(:month) - 1) AS SIGNED) = 10 AND 月份 BETWEEN 1 AND 10 THEN IFNULL(每月处置金额, 0)
                                           WHEN CAST(SUBSTRING(:month, 1, LENGTH(:month) - 1) AS SIGNED) = 9 AND 月份 BETWEEN 1 AND 9 THEN IFNULL(每月处置金额, 0)
                                           WHEN CAST(SUBSTRING(:month, 1, LENGTH(:month) - 1) AS SIGNED) = 8 AND 月份 BETWEEN 1 AND 8 THEN IFNULL(每月处置金额, 0)
                                           WHEN CAST(SUBSTRING(:month, 1, LENGTH(:month) - 1) AS SIGNED) = 7 AND 月份 BETWEEN 1 AND 7 THEN IFNULL(每月处置金额, 0)
                                           WHEN CAST(SUBSTRING(:month, 1, LENGTH(:month) - 1) AS SIGNED) = 6 AND 月份 BETWEEN 1 AND 6 THEN IFNULL(每月处置金额, 0)
                                           WHEN CAST(SUBSTRING(:month, 1, LENGTH(:month) - 1) AS SIGNED) = 5 AND 月份 BETWEEN 1 AND 5 THEN IFNULL(每月处置金额, 0)
                                           WHEN CAST(SUBSTRING(:month, 1, LENGTH(:month) - 1) AS SIGNED) = 4 AND 月份 BETWEEN 1 AND 4 THEN IFNULL(每月处置金额, 0)
                                           WHEN CAST(SUBSTRING(:month, 1, LENGTH(:month) - 1) AS SIGNED) = 3 AND 月份 BETWEEN 1 AND 3 THEN IFNULL(每月处置金额, 0)
                                           WHEN CAST(SUBSTRING(:month, 1, LENGTH(:month) - 1) AS SIGNED) = 2 AND 月份 BETWEEN 1 AND 2 THEN IFNULL(每月处置金额, 0)
                                           WHEN CAST(SUBSTRING(:month, 1, LENGTH(:month) - 1) AS SIGNED) = 1 AND 月份 = 1 THEN IFNULL(每月处置金额, 0)
                                           ELSE 0
                                       END) AS 累计处置金额
                                   FROM 处置表
                                   WHERE (:year IS NULL OR 年份 = :year)
                                   AND (:company IS NULL OR :company = '所有公司' OR 管理公司 = :company)
                                   GROUP BY 管理公司, 债权人, 债务人, 期间, 是否涉诉
                                   HAVING 累计处置金额 > 0
                                   ORDER BY 累计处置金额 DESC
                               ) AS derived_table
                   """, nativeQuery = true)
    List<Map<String, Object>> findReductionDebtDetailList(
            @Param("year") String year,
            @Param("month") String month,
            @Param("company") String company);
}
