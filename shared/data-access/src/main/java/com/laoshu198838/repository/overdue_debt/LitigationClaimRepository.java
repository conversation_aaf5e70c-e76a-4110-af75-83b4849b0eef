package com.laoshu198838.repository.overdue_debt;

import com.laoshu198838.annotation.DataSource;
import com.laoshu198838.entity.overdue_debt.LitigationClaim;
import com.laoshu198838.entity.overdue_debt.LitigationClaim.LitigationCompositeKey;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * 诉讼债权表数据访问接口
 * 统一的Repository，替代各模块中的重复Repository
 * <AUTHOR>
 */
@Repository
@DataSource("primary")
public interface LitigationClaimRepository extends JpaRepository<LitigationClaim, LitigationCompositeKey> {

    /**
     * 批量更新"本年度累计回收"字段
     *
     * @return 更新的记录数
     */
    @Modifying
    @Transactional
    @Query(value = """
                   UPDATE 诉讼表 t1
                   JOIN (
                       SELECT
                           债权人,
                           债务人,
                           期间,
                           年份,
                           月份,
                           SUM(本月处置债权) OVER (
                               PARTITION BY 债权人, 债务人, 期间, 年份
                               ORDER BY 月份
                               ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
                           ) AS 累计处置
                       FROM 诉讼表
                   ) t2
                   ON t1.债权人 = t2.债权人
                      AND t1.债务人 = t2.债务人
                      AND t1.期间 = t2.期间
                      AND t1.年份 = t2.年份
                      AND t1.月份 = t2.月份
                   SET t1.本年度累计回收 = t2.累计处置
                   WHERE t1.本年度累计回收 <> t2.累计处置
                     AND t2.累计处置 IS NOT NULL
                   """, nativeQuery = true)
    int updateAnnualCumulativeRecovery();

    /**
     * 根据年份和月份查询诉讼表中的新增债权总额
     *
     * @param year  年份
     * @param month 月份
     * @return 新增债权总额
     */
    @Query("SELECT COALESCE(SUM(l.currentMonthNewDebt), 0) FROM LitigationClaim l WHERE l.id.year = :year AND l.id.month = :month")
    BigDecimal sumCurrentMonthNewDebtByYearAndMonth(@Param("year") int year, @Param("month") int month);

    /**
     * 查询诉讼表中的最大序号
     *
     * @return 最大序号，如果表为空返回null
     */
    @Query("SELECT MAX(l.sequence) FROM LitigationClaim l")
    Integer findMaxSequence();

    /**
     * 根据年份和月份查询诉讼表中的数据
     *
     * @param year  年份
     * @param month 月份
     * @return 诉讼表记录列表
     */
    @Query("SELECT l FROM LitigationClaim l WHERE l.id.year = :year AND l.id.month = :month")
    List<LitigationClaim> findByYearAndMonth(@Param("year") int year, @Param("month") int month);

    /**
     * 根据债权人、债务人、年份、月份和期间查询诉讼表记录
     *
     * @param creditor 债权人
     * @param debtor   债务人
     * @param year     年份
     * @param month    月份
     * @param period   期间（如"2025年新增债权"）
     * @return 匹配的诉讼表记录，如果存在
     */
    @Query("SELECT l FROM LitigationClaim l WHERE l.id.creditor = :creditor AND l.id.debtor = :debtor AND l.id.year = :year AND l.id.month = :month AND l.id.period = :period")
    Optional<LitigationClaim> findByCreditorAndDebtorAndYearAndMonthAndPeriod(
            @Param("creditor") String creditor,
            @Param("debtor") String debtor,
            @Param("year") Integer year,
            @Param("month") Integer month,
            @Param("period") String period);

    /**
     * 根据债权人和债务人查询诉讼表记录
     *
     * @param creditor 债权人
     * @param debtor   债务人
     * @return 匹配的诉讼表记录列表
     */
    @Query("SELECT l FROM LitigationClaim l WHERE l.id.creditor = :creditor AND l.id.debtor = :debtor")
    List<LitigationClaim> findByCreditorAndDebtor(@Param("creditor") String creditor, @Param("debtor") String debtor);

    /**
     * 检查指定年月是否有诉讼表数据
     *
     * @param year  年份
     * @param month 月份
     * @return 数据条数
     */
    @Query("SELECT COUNT(l) FROM LitigationClaim l WHERE l.id.year = :year AND l.id.month = :month")
    long countByYearAndMonthForExists(@Param("year") int year, @Param("month") int month);

    /**
     * 获取指定年月的诉讼表数据条数
     *
     * @param year  年份
     * @param month 月份
     * @return 数据条数
     */
    @Query("SELECT COUNT(l) FROM LitigationClaim l WHERE l.id.year = :year AND l.id.month = :month")
    long countByYearAndMonth(@Param("year") int year, @Param("month") int month);

    /**
     * 查询所有诉讼债权记录
     *
     * @return 所有诉讼债权记录列表
     */
    @Query("SELECT l FROM LitigationClaim l")
    List<LitigationClaim> findAllLitigationClaims();
}
