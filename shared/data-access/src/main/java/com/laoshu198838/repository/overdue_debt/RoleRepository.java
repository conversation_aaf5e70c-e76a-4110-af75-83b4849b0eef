package com.laoshu198838.repository.overdue_debt;

import com.laoshu198838.annotation.DataSource;
import com.laoshu198838.entity.overdue_debt.Role;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * 角色数据访问接口
 * 访问主数据库（逾期债权数据库）中的角色数据
 * <AUTHOR>
 */
@Repository
@DataSource("primary")
public interface RoleRepository extends JpaRepository<Role, Integer> {
    
    /**
     * 根据角色ID查找角色
     * @param roleId 角色ID
     * @return 角色信息
     */
    Role findByRoleId(Integer roleId);
}
