package com.laoshu198838.repository.overdue_debt;

import com.laoshu198838.annotation.DataSource;
import com.laoshu198838.entity.overdue_debt.ImpairmentReserve;
import com.laoshu198838.entity.overdue_debt.ImpairmentReserve.ImpairmentReserveKey;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * 减值准备表数据访问接口
 * 统一的Repository，替代各模块中的重复Repository
 * <AUTHOR>
 */
@Repository
@DataSource("primary")
public interface ImpairmentReserveRepository extends JpaRepository<ImpairmentReserve, ImpairmentReserveKey> {

    /**
     * 批量更新"本年度累计回收"字段
     *
     * @return 更新的记录数
     */
    @Modifying
    @Transactional
    @Query(value = """
                   UPDATE 减值准备表 t1
                   JOIN (
                       SELECT
                           债权人,
                           债务人,
                           期间,
                           年份,
                           月份,
                           SUM(本月处置债权) OVER (
                               PARTITION BY 债权人, 债务人, 期间, 年份
                               ORDER BY 月份
                               ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
                           ) AS 累计处置
                       FROM 减值准备表
                   ) t2
                   ON t1.债权人 = t2.债权人
                      AND t1.债务人 = t2.债务人
                      AND t1.期间 = t2.期间
                      AND t1.年份 = t2.年份
                      AND t1.月份 = t2.月份
                   SET t1.本年度累计回收 = t2.累计处置
                   WHERE t1.本年度累计回收 <> t2.累计处置
                     AND t2.累计处置 IS NOT NULL
                   """, nativeQuery = true)
    int updateAnnualCumulativeRecovery();

    /**
     * 根据年份和月份查询减值准备表中的新增债权总额
     *
     * @param year  年份
     * @param month 月份
     * @return 新增债权总额
     */
    @Query("SELECT COALESCE(SUM(i.currentMonthNewDebt), 0) FROM ImpairmentReserve i WHERE i.id.year = :year AND i.id.month = :month")
    BigDecimal sumCurrentMonthNewDebtByYearAndMonth(@Param("year") int year, @Param("month") int month);

    /**
     * 根据年份和月份查询减值准备表中的数据
     *
     * @param year  年份
     * @param month 月份
     * @return 减值准备表记录列表
     */
    @Query("SELECT i FROM ImpairmentReserve i WHERE i.id.year = :year AND i.id.month = :month")
    List<ImpairmentReserve> findByIdYearAndIdMonth(@Param("year") int year, @Param("month") int month);

    /**
     * 根据债权人和债务人查询减值准备表记录
     *
     * @param creditor 债权人
     * @param debtor   债务人
     * @return 匹配的减值准备表记录列表，按年月降序排序
     */
    @Query("SELECT i FROM ImpairmentReserve i WHERE i.id.creditor = :creditor AND i.id.debtor = :debtor ORDER BY i.id.year DESC, i.id.month DESC")
    List<ImpairmentReserve> findByIdCreditorAndIdDebtor(@Param("creditor") String creditor, @Param("debtor") String debtor);

    /**
     * 根据年份查询减值准备表处置记录
     *
     * @param year 年份
     * @return 匹配的减值准备表记录列表
     */
    @Query("SELECT i FROM ImpairmentReserve i " +
           "WHERE i.id.year = :year " +
           "  AND i.currentMonthDisposeDebt <> 0 " +
           "ORDER BY i.id.month ASC, i.currentMonthDisposeDebt DESC")
    List<ImpairmentReserve> findDecreaseByYear(@Param("year") int year);

    /**
     * 根据年份查询减值准备表新增记录
     *
     * @param year 年份
     * @return 匹配的减值准备表记录列表
     */
    @Query("SELECT i FROM ImpairmentReserve i " +
           "WHERE i.id.year = :year " +
           "  AND i.currentMonthNewDebt <> 0 " +
           "ORDER BY i.id.month ASC, i.currentMonthNewDebt DESC")
    List<ImpairmentReserve> findAddByYear(@Param("year") int year);
}
