package com.laoshu198838.repository.overdue_debt;

import com.laoshu198838.annotation.DataSource;
import com.laoshu198838.entity.overdue_debt.NonLitigationClaim;
import com.laoshu198838.entity.overdue_debt.NonLitigationClaim.NonLitigationCompositeKey;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * 非诉讼债权表数据访问接口
 * 统一的Repository，替代各模块中的重复Repository
 * <AUTHOR>
 */
@Repository
@DataSource("primary")
public interface NonLitigationClaimRepository extends JpaRepository<NonLitigationClaim, NonLitigationCompositeKey> {

    /**
     * 更新本年度累计回收金额
     * 通过计算每个债权人和债务人在当年的累计处置债权金额来更新本年度累计回收字段
     *
     * @return 更新的记录数
     */
    @Modifying
    @Transactional
    @Query(value = """
                   UPDATE 非诉讼表 t1
                   JOIN (
                       SELECT
                           债权人,
                           债务人,
                           期间,
                           年份,
                           月份,
                           SUM(本月处置债权) OVER (
                               PARTITION BY 债权人, 债务人, 期间, 年份
                               ORDER BY 月份
                               ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
                           ) AS 累计处置
                       FROM 非诉讼表
                   ) t2
                   ON t1.债权人 = t2.债权人
                      AND t1.债务人 = t2.债务人
                      AND t1.期间 = t2.期间
                      AND t1.年份 = t2.年份
                      AND t1.月份 = t2.月份
                   SET t1.本年度累计回收 = t2.累计处置
                   WHERE t1.本年度累计回收 <> t2.累计处置
                     AND t2.累计处置 IS NOT NULL
                   """, nativeQuery = true)
    int updateAnnualCumulativeRecovery();

    /**
     * 根据年份和月份查询非诉讼表中的新增债权总额
     * 通过计算本月本金增减、本月利息增减、本月违约金增减的总和来获取
     *
     * @param year  年份
     * @param month 月份
     * @return 新增债权总额
     */
    @Query("SELECT COALESCE(SUM(n.currentMonthPrincipalIncreaseDecrease + " +
           "n.currentMonthInterestIncreaseDecrease + " +
           "n.currentMonthPenaltyIncreaseDecrease), 0) " +
           "FROM NonLitigationClaim n WHERE n.id.year = :year AND n.id.month = :month")
    BigDecimal sumCurrentMonthIncreaseByYearAndMonth(@Param("year") int year, @Param("month") int month);

    /**
     * 根据年份和月份查询非诉讼表中的数据
     *
     * @param year  年份
     * @param month 月份
     * @return 非诉讼表记录列表
     */
    @Query("SELECT n FROM NonLitigationClaim n WHERE n.id.year = :year AND n.id.month = :month")
    List<NonLitigationClaim> findByYearAndMonth(@Param("year") int year, @Param("month") int month);

    /**
     * 根据债权人、债务人、年份、月份和期间查询非诉讼表记录
     *
     * @param creditor 债权人
     * @param debtor   债务人
     * @param year     年份
     * @param month    月份
     * @param period   期间（如"2025年04月"）
     * @return 匹配的非诉讼表记录，如果存在
     */
    @Query("SELECT n FROM NonLitigationClaim n WHERE n.id.creditor = :creditor AND n.id.debtor = :debtor AND n.id.year = :year AND n.id.month = :month AND n.id.period = :period")
    Optional<NonLitigationClaim> findByCreditorAndDebtorAndYearAndMonthAndPeriod(
            @Param("creditor") String creditor,
            @Param("debtor") String debtor,
            @Param("year") Integer year,
            @Param("month") Integer month,
            @Param("period") String period);

    /**
     * 根据债权人、债务人、年份和月份查询非诉讼表记录
     *
     * @param creditor 债权人
     * @param debtor   债务人
     * @param year     年份
     * @param month    月份
     * @return 匹配的非诉讼表记录，如果存在
     */
    @Query("SELECT n FROM NonLitigationClaim n WHERE n.id.creditor = :creditor AND n.id.debtor = :debtor AND n.id.year = :year AND n.id.month = :month")
    Optional<NonLitigationClaim> findByCreditorAndDebtorAndYearAndMonth(
            @Param("creditor") String creditor,
            @Param("debtor") String debtor,
            @Param("year") Integer year,
            @Param("month") Integer month);
}
