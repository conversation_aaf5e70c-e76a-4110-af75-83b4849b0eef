package com.laoshu198838.constant;

/**
 * 债权管理系统常量类
 * 
 * <p>定义系统中使用的各种常量，包括数据库相关、业务状态、日期格式等。</p>
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
public final class DebtManagementConstants {
    
    /**
     * 私有构造函数，防止实例化常量类
     */
    private DebtManagementConstants() {
        throw new UnsupportedOperationException("Constants class cannot be instantiated");
    }
    
    // ==================== 数据库相关常量 ====================
    
    /**
     * 主数据库名称
     */
    public static final String PRIMARY_DB_NAME = "逾期债权数据库";
    
    /**
     * 金蝶数据库名称
     */
    public static final String KINGDEE_DB_NAME = "kingdee";
    
    // ==================== 业务状态常量 ====================
    
    /**
     * 用户状态：激活
     */
    public static final String STATUS_ACTIVE = "ACTIVE";
    
    /**
     * 用户状态：非激活
     */
    public static final String STATUS_INACTIVE = "INACTIVE";
    
    /**
     * 默认公司名称
     */
    public static final String DEFAULT_COMPANY = "所有公司";
    
    /**
     * 默认部门名称
     */
    public static final String DEFAULT_DEPARTMENT = "所有部门";
    
    // ==================== 债权类型常量 ====================
    
    /**
     * 涉诉状态：是
     */
    public static final String LITIGATION_YES = "是";
    
    /**
     * 涉诉状态：否
     */
    public static final String LITIGATION_NO = "否";
    
    /**
     * 债权类型：诉讼债权
     */
    public static final String DEBT_TYPE_LITIGATION = "诉讼债权";
    
    /**
     * 债权类型：非诉讼债权
     */
    public static final String DEBT_TYPE_NON_LITIGATION = "非诉讼债权";
    
    // ==================== 日期格式常量 ====================
    
    /**
     * 标准日期格式：yyyy-MM-dd
     */
    public static final String DATE_FORMAT_YYYY_MM_DD = "yyyy-MM-dd";
    
    /**
     * 年月格式：yyyy-MM
     */
    public static final String DATE_FORMAT_YYYY_MM = "yyyy-MM";
    
    /**
     * 中文日期格式：yyyy年MM月dd日
     */
    public static final String DATE_FORMAT_CHINESE_FULL = "yyyy年MM月dd日";
    
    /**
     * 中文年月格式：yyyy年MM月
     */
    public static final String DATE_FORMAT_CHINESE_MONTH = "yyyy年MM月";
    
    // ==================== 文件相关常量 ====================
    
    /**
     * Excel文件扩展名
     */
    public static final String EXCEL_EXTENSION = ".xlsx";
    
    /**
     * YAML文件扩展名
     */
    public static final String YAML_EXTENSION = ".yaml";
    
    // ==================== 数值常量 ====================
    
    /**
     * 金额限制：10万元
     */
    public static final int AMOUNT_LIMIT_10W = 10;
    
    /**
     * 默认重试次数
     */
    public static final int DEFAULT_RETRY_COUNT = 3;
    
    /**
     * 默认重试间隔（毫秒）
     */
    public static final long DEFAULT_RETRY_INTERVAL = 2000L;
    
    // ==================== 表名常量 ====================
    
    /**
     * 数据库表名：新增表
     */
    public static final String TABLE_OVERDUE_DEBT_ADD = "新增表";
    
    /**
     * 数据库表名：处置表
     */
    public static final String TABLE_OVERDUE_DEBT_DECREASE = "处置表";
    
    /**
     * 数据库表名：诉讼表
     */
    public static final String TABLE_LITIGATION_CLAIM = "诉讼表";
    
    /**
     * 数据库表名：非诉讼表
     */
    public static final String TABLE_NON_LITIGATION_CLAIM = "非诉讼表";
    
    /**
     * 数据库表名：减值准备表
     */
    public static final String TABLE_IMPAIRMENT_RESERVE = "减值准备表";
    
    /**
     * 数据库表名：用户表
     */
    public static final String TABLE_USERS = "users";
    
    /**
     * 数据库表名：角色表
     */
    public static final String TABLE_ROLES = "roles";
    
    // ==================== 错误信息常量 ====================
    
    /**
     * 错误信息：数据库连接失败
     */
    public static final String ERROR_DB_CONNECTION_FAILED = "数据库连接失败";
    
    /**
     * 错误信息：文件未找到
     */
    public static final String ERROR_FILE_NOT_FOUND = "文件未找到";
    
    /**
     * 错误信息：参数无效
     */
    public static final String ERROR_INVALID_PARAMETER = "参数无效";
    
    /**
     * 错误信息：权限不足
     */
    public static final String ERROR_INSUFFICIENT_PERMISSION = "权限不足";
}
