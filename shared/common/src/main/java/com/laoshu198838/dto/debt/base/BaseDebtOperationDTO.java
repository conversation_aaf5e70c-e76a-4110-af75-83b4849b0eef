package com.laoshu198838.dto.debt.base;

import lombok.Data;

import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 债权操作基础DTO
 * 包含所有债权操作的公共字段
 *
 * <AUTHOR>
 */
@Data
public abstract class BaseDebtOperationDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 债权人
     */
    @NotBlank(message = "债权人不能为空")
    @Size(max = 30, message = "债权人名称不能超过30个字符")
    private String creditor;

    /**
     * 债务人
     */
    @NotBlank(message = "债务人不能为空")
    @Size(max = 30, message = "债务人名称不能超过30个字符")
    private String debtor;

    /**
     * 管理公司
     */
    @NotBlank(message = "管理公司不能为空")
    @Size(max = 30, message = "管理公司名称不能超过30个字符")
    private String managementCompany;

    /**
     * 是否涉诉
     */
    @NotBlank(message = "是否涉诉不能为空")
    @Pattern(regexp = "是|否", message = "是否涉诉只能为'是'或'否'")
    private String isLitigation;

    /**
     * 期间
     */
    @NotBlank(message = "期间不能为空")
    @Pattern(regexp = "^\\d{4}年(新增债权|430|\\d{1,2}月)$", 
            message = "期间格式不正确，应为：YYYY年新增债权 或 YYYY年430")
    private String period;

    /**
     * 年份
     */
    @NotNull(message = "年份不能为空")
    @Min(value = 2000, message = "年份不能小于2000")
    @Max(value = 2100, message = "年份不能大于2100")
    private Integer year;

    /**
     * 月份
     */
    @NotNull(message = "月份不能为空")
    @Min(value = 1, message = "月份必须在1-12之间")
    @Max(value = 12, message = "月份必须在1-12之间")
    private Integer month;

    /**
     * 金额
     */
    @NotNull(message = "金额不能为空")
    @DecimalMin(value = "0.01", message = "金额必须大于0")
    @Digits(integer = 13, fraction = 2, message = "金额格式不正确，整数部分最多13位，小数部分最多2位")
    private BigDecimal amount;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注不能超过500个字符")
    private String remark;

    /**
     * 操作人ID
     */
    private String operatorId;

    /**
     * 操作人姓名
     */
    private String operatorName;

    /**
     * 操作时间
     */
    private LocalDateTime operationTime;

    /**
     * 操作IP
     */
    private String operationIp;

    /**
     * 来源系统
     */
    private String sourceSystem;

    /**
     * 获取操作类型
     * 子类需要实现此方法返回具体的操作类型
     */
    public abstract String getOperationType();

    /**
     * 验证业务规则
     * 子类可以重写此方法添加特定的业务规则验证
     */
    public void validateBusinessRules() {
        // 基础验证逻辑
        if (operationTime == null) {
            operationTime = LocalDateTime.now();
        }
    }
}