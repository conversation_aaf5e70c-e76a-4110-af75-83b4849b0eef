package com.laoshu198838.export;

import java.io.File;
import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import com.aspose.cells.Cell;
import com.aspose.cells.Cells;
import com.aspose.cells.Workbook;
import com.aspose.cells.Worksheet;
import static com.laoshu198838.util.database.DatabaseUtils.getConnection;
import static com.laoshu198838.util.database.SqlUtils.executeQuery;
import static com.laoshu198838.util.database.SqlUtils.getResultSetColumns;
import static com.laoshu198838.util.date.DateUtils.getPreviousYearAndMonth;
import static com.laoshu198838.util.file.ExcelUtils.loadExcelTemplate;
import static com.laoshu198838.util.file.ExcelUtils.putValueWithFormat;
import static com.laoshu198838.util.file.ExcelUtils.saveWorkbook;
import static com.laoshu198838.util.file.YamlUtils.readSingleLevelYaml;

/**
 * <AUTHOR>
 */
public class ExcelExportOverdueDebt {

    /**
     * 程序入口点，调用excelExport方法导出逾期债权数据到Excel
     *
     * @param args 命令行参数（本程序未使用）
     * @throws Exception 如果导出过程中发生异常
     */
    public static void main(String[] args) throws Exception {
        excelExport();
    }

    /**
     * 导出逾期债权清收统计表（集团所需模版）
     * <p>
     * 该方法执行以下操作：
     * 1. 连接逾期债权数据库
     * 2. 加载Excel模板
     * 3. 导出诉讼表、非诉讼表、减值准备表等数据
     * 4. 将生成的Excel文件保存到桌面
     * </p>
     *
     * @throws Exception 如果导出过程中发生异常
     */
    public static void excelExport() throws Exception {
        // 创建服务实例
        ExcelExportOverdueDebt service = new ExcelExportOverdueDebt();

        // 连接overdue_debt_db
        Connection connection = getConnection("逾期债权数据库");
        if (connection == null) {
            System.err.println("❌ 数据库连接失败！");
            throw new Exception("无法连接到逾期债权数据库");
        }
        System.out.println("✅ 数据库连接成功");

        // 加载 Excel 模板文件
        Workbook workbook = loadExcelTemplate("逾期债权清收统计表（模版）.xlsx");
        if (workbook == null) {
            System.err.println("❌ Excel模板加载失败！");
            throw new Exception("无法加载Excel模板文件");
        }
        System.out.println("✅ Excel模板加载成功");

        // 设置要导出的年份和月份
        int year = 2025;
        int month = 2;
        // 10万元限额，用于表格7
        int amount = 10;
        try {
            // 导出各个表格数据
            // 导出诉讼表数据到表格3
            service.exportToTable3(connection, workbook, year, month);
            // 导出非诉讼表数据到表格4
            service.exportToTable4(connection, workbook, year, month);
            // 导出减值准备表数据到表格5
            service.exportToTable5(connection, workbook, year, month);

            // 风险准备金统计表（暂未启用）
            // service.exportToTable6(connection,workbook,year, month);
            // 10万元及以下应收债权明细表
            service.exportToTable7(connection, workbook, year, month, amount);
            // 临表-3
            service.exportToTable8(connection, workbook, year, month);
            // 新增债权明细表
            service.exportToTable9(connection, workbook, year, month);
            // 处置债权明细表
            service.exportToTable10(connection, workbook, year, month);

        } catch (SQLException e) {
            e.printStackTrace();
        }

        // 保存导出后的Excel到桌面
        String desktopPath = System.getProperty("user.home") + File.separator + "Desktop" + File.separator;
        String fileName = year + "年" + String.format("%02d", month) + "月逾期债权清收统计表-万润科技汇总.xlsx";

        saveWorkbook(workbook, desktopPath, fileName);
    }

    /**
     * 将指定年份、月份的诉讼表数据导出到Excel的表格3
     * <p>
     * 该方法从数据库中查询诉讼表数据，并将其写入Excel模板的“表3-涉诉”工作表中。
     * </p>
     *
     * @param connection 数据库连接对象
     * @param workbook   Excel工作簿对象
     * @param year       要导出的年份(例如 2024)
     * @param month      要导出的月份(1~12)
     * @throws SQLException 如果数据库查询过程中发生异常
     */
    public void exportToTable3(Connection connection, Workbook workbook, int year, int month) throws SQLException {
        // 设置数据库表名
        String databaseTableName = "诉讼表";
        // 设置Excel工作表名
        String excelTableName = "表3-涉诉";
        // 调用通用导出方法将数据导出到Excel
        exportTable(connection, databaseTableName, workbook, excelTableName, year, month);
    }

    /**
     * 将指定年份、月份的非诉讼表数据导出到Excel的表格4
     * <p>
     * 该方法从数据库中查询非诉讼表数据，并将其写入Excel模板的“表4-非涉诉”工作表中。
     * </p>
     *
     * @param connection 数据库连接对象
     * @param workbook   Excel工作簿对象
     * @param year       要导出的年份(例如 2024)
     * @param month      要导出的月份(1~12)
     * @throws SQLException 如果数据库查询过程中发生异常
     */
    public void exportToTable4(Connection connection, Workbook workbook, int year, int month) throws SQLException {
        // 设置数据库表名
        String databaseTableName = "非诉讼表";
        // 设置Excel工作表名
        String excelTableName = "表4-非涉诉";
        // 调用通用导出方法将数据导出到Excel
        exportTable(connection, databaseTableName, workbook, excelTableName, year, month);
    }

    /**
     * 将指定年份、月份的减值准备表数据导出到Excel的表格5
     * <p>
     * 该方法从数据库中查询减值准备表数据，并将其写入Excel模板的“表5-减值准备”工作表中。
     * </p>
     *
     * @param connection 数据库连接对象
     * @param workbook   Excel工作簿对象
     * @param year       要导出的年份(例如 2024)
     * @param month      要导出的月份(1~12)
     * @throws SQLException 如果数据库查询过程中发生异常
     */
    public void exportToTable5(Connection connection, Workbook workbook, int year, int month) throws SQLException {
        // 设置数据库表名
        String databaseTableName = "减值准备表";
        // 设置Excel工作表名
        String excelTableName = "表5-减值准备";
        // 调用通用导出方法将数据导出到Excel
        exportTable(connection, databaseTableName, workbook, excelTableName, year, month);
    }

    /**
     * 将风险准备金统计表数据导出到Excel的表格6
     * <p>
     * 该方法目前仅为占位，实际功能尚未实现。
     * </p>
     *
     * @param connection 数据库连接对象
     * @param workbook   Excel工作簿对象
     * @param year       要导出的年份
     * @param month      要导出的月份
     * @throws SQLException 如果数据库查询过程中发生异常
     */
    public void exportToTable6(Connection connection, Workbook workbook, int year, int month) throws SQLException {
        // 导出 Excel（功能尚未实现）
        System.out.println("export");
    }

    /**
     * 导出“表7-<amount>万元及以下应收债权明细表”到 Excel。
     *
     * <ul>
     *   <li>年初余额：上一年12月，不分案件名称，按债权人+债务人+是否涉诉汇总</li>
     *   <li>当年1~month月：按债权人+债务人+案件名称+是否涉诉+月份分组，筛选≤amount且≠0</li>
     *   <li>Excel 列：序号、债权主体、债务主体、案件名称、年初余额、1月～month月余额、当年累计清收处置、年新增、是否涉诉</li>
     * </ul>
     *
     * @param connection 已打开的 JDBC 连接，不能为空
     * @param workbook   Aspose.Cells 工作簿，不能为空
     * @param year       查询年度（如 2025）
     * @param month      截至月份（1~12）
     * @param amount     金额限额（万元）
     * @throws SQLException 当 SQL 执行或结果集处理失败时抛出
     */
    public void exportToTable7(
            Connection connection,
            Workbook workbook,
            int year,
            int month,
            int amount
                              ) throws Exception {
        // 设置数据库表名
        String databaseTableName = "减值准备表";
        // 设置Excel工作表名
        String excelTableName = "表7-10万元及以下应收债权明细表";
        // 调用通用导出方法将数据导出到Excel
        exportTable(connection, databaseTableName, workbook, excelTableName, year, month);
    }

    /**
     * 将临时表格3数据导出到Excel的表格8
     * <p>
     * 该方法目前仅为占位，实际功能尚未实现。
     * </p>
     *
     * @param connection 数据库连接对象
     * @param workbook   Excel工作簿对象
     * @param year       要导出的年份
     * @param month      要导出的月份
     * @throws SQLException 如果数据库查询过程中发生异常
     */
    public void exportToTable8(Connection connection, Workbook workbook, int year, int month) throws SQLException {
        // 设置数据库表名
        String databaseTableName = "处置表";
        // 设置Excel工作表名
        String excelTableName = "表8-临3表";
        // 调用通用导出方法将数据导出到Excel
        exportTable(connection, databaseTableName, workbook, excelTableName, year, month);
    }

    /**
     * 将新增债权明细表数据导出到Excel的表格9
     * <p>
     * 该方法目前仅为占位，实际功能尚未实现。
     * 完成后将从数据库中查询新增债权数据，并将其导出到Excel。
     * </p>
     *
     * @param connection 数据库连接对象
     * @param workbook   Excel工作簿对象
     * @param year       要导出的年份
     * @param month      要导出的月份
     * @throws SQLException 如果数据库查询过程中发生异常
     */
    public void exportToTable9(Connection connection, Workbook workbook, int year, int month) throws SQLException {
        // 导出 Excel（功能尚未实现）
        // 设置Excel工作表名
        String excelTableName = "表9-新增逾期债权明细表";
        // 指定查询标识符，与构建查询方法中的条件判断一致
        String tableName = "表9-新增逾期债权明细表";
        // 调用通用导出方法将数据导出到Excel
        exportTable(connection, tableName, workbook, excelTableName, year, month);
    }

    /**
     * 将处置债权明细表数据导出到Excel的表格10
     * <p>
     * 该方法目前仅为占位，实际功能尚未实现。
     * 完成后将从数据库中查询处置债权数据，并将其导出到Excel。
     * </p>
     *
     * @param connection 数据库连接对象
     * @param workbook   Excel工作簿对象
     * @param year       要导出的年份
     * @param month      要导出的月份
     * @throws SQLException 如果数据库查询过程中发生异常
     */
    public void exportToTable10(Connection connection, Workbook workbook, int year, int month) throws SQLException {
        // 导出 Excel（功能尚未实现）
        // 设置数据库表名
        String databaseTableName = "处置表";
        // 设置Excel工作表名
        String excelTableName = "表10-债权处置明细表";
        // 调用通用导出方法将数据导出到Excel
        exportTable(connection, databaseTableName, workbook, excelTableName, year, month);
    }


    /**
     * 从数据库表获取指定年份和月份的数据，并将其导出到指定的 Excel 工作表。
     * <p>
     * 该方法是各种导出方法的通用实现，执行以下操作：
     * 1. 根据年份查询指定数据库表的数据
     * 2. 获取Excel中对应的工作表
     * 3. 调用insertDataToExcel方法将数据插入到Excel中
     * </p>
     *
     * @param connection        数据库连接对象，用于执行 SQL 查询
     * @param databaseTableName 数据库中的表名（如 “诉讼表”、“非诉讼表” 等）
     * @param workbook          Excel 工作簿对象，包含多个工作表
     * @param excelTableName    目标 Excel 工作表的名称（如 “表3-涉诉”、“表4-非涉诉”）
     * @param year              需要查询的数据年份（如 2024）
     * @param month             需要查询的数据月份（1~12）
     */
    private void exportTable(Connection connection, String databaseTableName, Workbook workbook, String excelTableName, int year, int month) {
        // 检查数据库连接是否有效
        if (connection == null) {
            throw new IllegalArgumentException("数据库连接为空！");
        }
        AbstractMap.SimpleEntry<String, List<Object>> entry;
        // 构建 SQL 查询语句和参数列表
        if ("表7-10万元及以下应收债权明细表".equals(excelTableName) || "表10-债权处置明细表".equals(excelTableName) || "表9-新增逾期债权明细表".equals(excelTableName)) {
            entry = buildQueryAndParams(excelTableName, year, month);
        } else {
            entry = buildQueryAndParams(databaseTableName, year, month);
        }
        String sql = entry.getKey();
        List<Object> params = entry.getValue();


        // 执行 SQL 查询，并将结果插入到目标 Excel 工作表
        try (ResultSet rs = executeQuery(connection, sql, params.toArray())) {
            // 获取目标Excel工作表
            Worksheet sheet = workbook.getWorksheets().get(excelTableName);

            // 检查工作表是否存在
            if (sheet == null) {
                System.err.println("警告: 在Excel模板中没有找到工作表 '" + excelTableName + "'。请确保模板中包含此工作表。");
                return; // 如果工作表不存在，则跳过处理
            }

            // 将数据插入到Excel工作表中
            if ("表7-10万元及以下应收债权明细表".equals(excelTableName) || "表9-新增逾期债权明细表".equals(excelTableName) || "表10-债权处置明细表".equals(excelTableName)) {
                insertDataToExcel(excelTableName, year, month, sheet, rs);
            } else {
                insertDataToExcel(databaseTableName, year, month, sheet, rs);
            }
//            特殊处理表5-减值准备，同时将数据插入到"表5-减值准备(长投)"工作表
            if ("表5-减值准备".equals(excelTableName)) {
                try (ResultSet rs2 = executeQuery(connection, sql, params.toArray())) {
                    // 获取长投工作表
                    Worksheet longInvestSheet = workbook.getWorksheets().get("表5-减值准备(长投)");
                    if (longInvestSheet != null) {
                        // 将数据插入到长投工作表中，使用"减值准备 (长投)"作为列映射查找的依据
                        insertDataToExcel("减值准备(长投)", year, month, longInvestSheet, rs2);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }


    }

    /**
     * 构建 SQL 查询语句和参数列表
     *
     * @param tableName 表名，如“处置表”或其他任意数据表
     * @param year      当前年份
     * @param month     当前月份
     * @return 一个包含 SQL 和参数列表的 Map.Entry
     */
    private static AbstractMap.SimpleEntry<String, List<Object>> buildQueryAndParams(String tableName, int year, int month) {
        String sql;
        List<Object> params = new ArrayList<>();

        if (Objects.equals(tableName, "处置表")) {
            sql = """
                  WITH
                    year_start_balance AS (
                      SELECT
                        管理公司,
                        债权人,
                        债务人,
                        是否涉诉,
                        科目名称,
                        期间,
                        SUM(本月末债权余额)                      AS 上年末余额,
                        GROUP_CONCAT(DISTINCT 备注 SEPARATOR ',') AS 备注
                      FROM 减值准备表
                      WHERE 年份 = ? AND 月份 = 12
                      GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
                      HAVING SUM(本月末债权余额) <> 0
                    ),
                    new_debt AS (
                      SELECT
                        管理公司,
                        债权人,
                        债务人,
                        是否涉诉,
                        科目名称,
                        期间,
                        SUM(本月新增债权) AS 当年累计新增债权
                      FROM 减值准备表
                      WHERE 年份 = ? AND 月份 <= ?
                      GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
                    ),
                    disposal AS (
                      SELECT
                        管理公司,
                        债权人,
                        债务人,
                        是否涉诉,
                        期间,
                        SUM(每月处置金额) AS 当年累计处置金额,
                        SUM(现金处置)     AS 现金处置,
                        SUM(资产抵债)     AS 资产抵债,
                        SUM(分期还款 + 其他方式) AS 其他方式
                      FROM 处置表
                      WHERE 年份 = ? AND 月份 <= ?
                      GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 期间
                    ),
                    all_keys AS (
                      SELECT 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间 FROM year_start_balance
                      UNION
                      SELECT 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间 FROM new_debt
                    )
                  SELECT
                    k.管理公司,
                    k.债权人,
                    k.债务人,
                    k.是否涉诉,
                    k.科目名称,
                    k.期间,
                    COALESCE(y.上年末余额,     0) AS 上年末余额,
                    COALESCE(n.当年累计新增债权, 0) AS 当年新增债权,
                    COALESCE(d.当年累计处置金额, 0) AS 当年累计处置金额,
                    COALESCE(d.现金处置,        0) AS 现金处置,
                    COALESCE(d.资产抵债,        0) AS 资产抵债,
                    COALESCE(d.其他方式,        0) AS 其他方式,
                    COALESCE(y.备注,           '') AS 备注
                  FROM all_keys k
                  LEFT JOIN year_start_balance y
                    ON k.管理公司 = y.管理公司 AND k.债权人 = y.债权人 AND k.债务人 = y.债务人
                    AND k.是否涉诉 = y.是否涉诉 AND k.科目名称 = y.科目名称 AND k.期间 = y.期间
                  LEFT JOIN new_debt n
                    ON k.管理公司 = n.管理公司 AND k.债权人 = n.债权人 AND k.债务人 = n.债务人
                    AND k.是否涉诉 = n.是否涉诉 AND k.科目名称 = n.科目名称 AND k.期间 = n.期间
                  LEFT JOIN disposal d
                    ON k.管理公司 = d.管理公司 AND k.债权人 = d.债权人 AND k.债务人 = d.债务人
                    AND k.是否涉诉 = d.是否涉诉 AND k.期间 = d.期间
                  WHERE
                    COALESCE(y.上年末余额, 0) <> 0 OR COALESCE(n.当年累计新增债权, 0) <> 0
                  ORDER BY 上年末余额 DESC;
                  """;
            params.add(year - 1);      // CTE year_start_balance 的年份，例如 2024
            params.add(year);    // CTE new_debt 的年份，例如 2025
            params.add(month);   // CTE new_debt 的月份上限，例如 1
            params.add(year);    // CTE disposal 的年份，例如 2025
            params.add(month);   // CTE disposal 的月份上限，例如 1
        } else if ("表7-10万元及以下应收债权明细表".equals(tableName)) {
            // 10万元以下表格筛选的sql
            sql = """
                  WITH filtered_data AS (
                      -- 筛选出上年12月和当年1月至指定月份的数据，且债权余额≤10万
                      SELECT
                          债权人,
                          债务人,
                          是否涉诉,
                          期间,
                          年份,
                          月份,
                          本月末债权余额 AS 月末余额,
                          本年度累计回收
                      FROM 逾期债权数据库.减值准备表
                      WHERE ((年份 = ? - 1 AND 月份 = 12) OR (年份 = ? AND 月份 BETWEEN 1 AND ?))
                        AND 本月末债权余额 > 0           -- 大于0的余额
                        AND 本月末债权余额 <= 10  -- 10万元限制
                  ),
                  init_balance AS (
                      -- 计算年初余额（上年12月的余额）
                      SELECT
                          债权人,
                          债务人,
                          是否涉诉,
                          SUM(月末余额) AS 年初余额
                      FROM filtered_data
                      WHERE 年份 = ? - 1 AND 月份 = 12
                      GROUP BY 债权人, 债务人, 是否涉诉
                  ),
                  current_year_data AS (
                      -- 当年各月数据
                      SELECT
                          fd.债权人,
                          fd.债务人,
                          fd.是否涉诉,
                          fd.期间,
                          fd.月份,
                          fd.月末余额,
                          fd.本年度累计回收,
                          -- 按债权人+债务人+是否涉诉分组，并按月份降序排序，取最大月份的累计回收
                          ROW_NUMBER() OVER (PARTITION BY fd.债权人, fd.债务人, fd.是否涉诉 ORDER BY fd.月份 DESC) AS month_rank
                      FROM filtered_data fd
                      WHERE fd.年份 = ?
                  ),
                  final_data AS (
                      -- 将当前年度数据与年初余额合并
                      SELECT
                          cyd.债权人,
                          cyd.债务人,
                          cyd.是否涉诉,
                          cyd.期间,
                          cyd.月份,
                          cyd.月末余额,
                          ib.年初余额,
                          -- 只保留最大月份的累计回收
                          CASE WHEN cyd.month_rank = 1 THEN cyd.本年度累计回收 ELSE NULL END AS 本年度累计回收,
                          -- 如果年初余额为0或NULL则标记为新增
                          CASE WHEN ib.年初余额 IS NULL OR ib.年初余额 = 0 THEN '是' ELSE '否' END AS 是否新增
                      FROM current_year_data cyd
                      LEFT JOIN init_balance ib ON cyd.债权人 = ib.债权人 AND cyd.债务人 = ib.债务人 AND cyd.是否涉诉 = ib.是否涉诉
                  ),
                  pivoted_data AS (
                      -- 透视数据，为每个月创建单独的列
                      SELECT
                          fd.债权人,
                          fd.债务人,
                          fd.是否涉诉,
                          fd.期间,
                          -- 对于非聚合列，使用聚合函数处理
                          MAX(fd.年初余额) AS 年初余额,
                          MAX(fd.本年度累计回收) AS 本年度累计回收,
                          MAX(fd.是否新增) AS 是否新增,
                          -- 动态创建1月到12月的列（只有当月份匹配时才返回余额值）
                          MAX(CASE WHEN fd.月份 = 1 THEN fd.月末余额 ELSE NULL END) AS '1月',
                          MAX(CASE WHEN fd.月份 = 2 THEN fd.月末余额 ELSE NULL END) AS '2月',
                          MAX(CASE WHEN fd.月份 = 3 THEN fd.月末余额 ELSE NULL END) AS '3月',
                          MAX(CASE WHEN fd.月份 = 4 THEN fd.月末余额 ELSE NULL END) AS '4月',
                          MAX(CASE WHEN fd.月份 = 5 THEN fd.月末余额 ELSE NULL END) AS '5月',
                          MAX(CASE WHEN fd.月份 = 6 THEN fd.月末余额 ELSE NULL END) AS '6月',
                          MAX(CASE WHEN fd.月份 = 7 THEN fd.月末余额 ELSE NULL END) AS '7月',
                          MAX(CASE WHEN fd.月份 = 8 THEN fd.月末余额 ELSE NULL END) AS '8月',
                          MAX(CASE WHEN fd.月份 = 9 THEN fd.月末余额 ELSE NULL END) AS '9月',
                          MAX(CASE WHEN fd.月份 = 10 THEN fd.月末余额 ELSE NULL END) AS '10月',
                          MAX(CASE WHEN fd.月份 = 11 THEN fd.月末余额 ELSE NULL END) AS '11月',
                          MAX(CASE WHEN fd.月份 = 12 THEN fd.月末余额 ELSE NULL END) AS '12月'
                      FROM final_data fd
                      GROUP BY fd.债权人, fd.债务人, fd.是否涉诉, fd.期间
                  )
                  -- 最终查询结果
                  SELECT
                      债权人,
                      债务人,
                      是否涉诉,
                      期间,
                      COALESCE(年初余额, 0) AS 年初余额,
                      COALESCE(年初余额, 0) AS 截至2024年底应收余额,
                      `1月`, `2月`, `3月`, `4月`, `5月`, `6月`,
                      `7月`, `8月`, `9月`, `10月`, `11月`, `12月`,
                      本年度累计回收 AS 累计清收,
                      是否新增
                  FROM pivoted_data
                  WHERE
                      -- 确保各月份的值（如果不为Null）都小于等于10万元
                      (COALESCE(`1月`, 0) <= 10) AND
                      (COALESCE(`2月`, 0) <= 10) AND
                      (COALESCE(`3月`, 0) <= 10) AND
                      (COALESCE(`4月`, 0) <= 10) AND
                      (COALESCE(`5月`, 0) <= 10) AND
                      (COALESCE(`6月`, 0) <= 10) AND
                      (COALESCE(`7月`, 0) <= 10) AND
                      (COALESCE(`8月`, 0) <= 10) AND
                      (COALESCE(`9月`, 0) <= 10) AND
                      (COALESCE(`10月`, 0) <= 10) AND
                      (COALESCE(`11月`, 0) <= 10) AND
                      (COALESCE(`12月`, 0) <= 10) AND
                      -- 至少一个月份的值大于0
                      (COALESCE(`1月`, 0) > 0 OR
                       COALESCE(`2月`, 0) > 0 OR
                       COALESCE(`3月`, 0) > 0 OR
                       COALESCE(`4月`, 0) > 0 OR
                       COALESCE(`5月`, 0) > 0 OR
                       COALESCE(`6月`, 0) > 0 OR
                       COALESCE(`7月`, 0) > 0 OR
                       COALESCE(`8月`, 0) > 0 OR
                       COALESCE(`9月`, 0) > 0 OR
                       COALESCE(`10月`, 0) > 0 OR
                       COALESCE(`11月`, 0) > 0 OR
                       COALESCE(`12月`, 0) > 0)
                  ORDER BY 债权人, 债务人, 是否涉诉
                  """;

            // 添加查询参数
            params.add(year); // 上一年
            params.add(year);     // 当前年
            params.add(month);    // 截止月份
            params.add(year); // 上一年（用于年初余额计算）
            params.add(year);     // 当前年（用于当年数据筛选）
        } else if ("表10-债权处置明细表".equals(tableName)) {
            // 表10的sql语句 - 处置明细报表
            sql = """
                  WITH
                    处置数据_1到当月 AS (
                      SELECT
                        债权人 COLLATE utf8mb4_general_ci AS 债权人,
                        债务人 COLLATE utf8mb4_general_ci AS 债务人,
                        是否涉诉 COLLATE utf8mb4_general_ci AS 是否涉诉,
                        期间 COLLATE utf8mb4_general_ci AS 期间,
                        管理公司 COLLATE utf8mb4_general_ci AS 管理公司,
                        SUM(每月处置金额) AS 累计处置金额,
                        SUM(现金处置) AS 累计现金回款,
                        MAX(CASE WHEN 分期还款 > 0 THEN '有分期还款' ELSE '' END) AS 分期还款说明,
                        MAX(CASE WHEN 资产抵债 > 0 THEN '有资产抵债' ELSE '' END) AS 资产抵债说明,
                        备注 COLLATE utf8mb4_general_ci AS 备注
                      FROM 处置表
                      WHERE 年份 = ? AND 月份 BETWEEN 1 AND ?
                      GROUP BY 债权人, 债务人, 是否涉诉, 期间, 管理公司, 备注
                    ),
                    处置数据_1到上月 AS (
                      SELECT
                        债权人 COLLATE utf8mb4_general_ci AS 债权人,
                        债务人 COLLATE utf8mb4_general_ci AS 债务人,
                        是否涉诉 COLLATE utf8mb4_general_ci AS 是否涉诉,
                        期间 COLLATE utf8mb4_general_ci AS 期间,
                        管理公司 COLLATE utf8mb4_general_ci AS 管理公司,
                        SUM(每月处置金额) AS 上月处置金额,
                        SUM(现金处置) AS 上月现金回款,
                        备注 COLLATE utf8mb4_general_ci AS 备注
                      FROM 处置表
                      WHERE 年份 = ? AND 月份 BETWEEN 1 AND ?
                      GROUP BY 债权人, 债务人, 是否涉诉, 期间, 管理公司, 备注
                    ),
                    合并数据 AS (
                      SELECT
                        a.债权人, a.债务人, a.是否涉诉, a.期间, a.管理公司,
                        a.累计处置金额, a.累计现金回款,
                        b.上月处置金额, b.上月现金回款,
                        CASE
                          WHEN a.分期还款说明 <> '' OR a.资产抵债说明 <> ''
                          THEN CONCAT(COALESCE(a.备注, ''), ' ', a.分期还款说明, ' ', a.资产抵债说明)
                          ELSE COALESCE(a.备注, b.备注, '')
                        END AS 备注
                      FROM 处置数据_1到当月 a
                      LEFT JOIN 处置数据_1到上月 b
                        ON a.债权人 = b.债权人 AND a.债务人 = b.债务人
                        AND a.是否涉诉 = b.是否涉诉 AND a.期间 = b.期间
                      UNION
                      SELECT
                        b.债权人, b.债务人, b.是否涉诉, b.期间, b.管理公司,
                        NULL AS 累计处置金额, NULL AS 累计现金回款,
                        b.上月处置金额, b.上月现金回款, b.备注
                      FROM 处置数据_1到上月 b
                      LEFT JOIN 处置数据_1到当月 a
                        ON a.债权人 = b.债权人 AND a.债务人 = b.债务人
                        AND a.是否涉诉 = b.是否涉诉 AND a.期间 = b.期间
                      WHERE a.债权人 IS NULL
                    ),
                    减值数据 AS (
                      SELECT
                        债权人 COLLATE utf8mb4_general_ci AS 债权人,
                        债务人 COLLATE utf8mb4_general_ci AS 债务人,
                        是否涉诉 COLLATE utf8mb4_general_ci AS 是否涉诉,
                        期间 COLLATE utf8mb4_general_ci AS 期间,
                        本月末债权余额
                      FROM 减值准备表
                      WHERE 年份 = ? AND 月份 = ?
                    )
                  SELECT
                    m.债权人,
                    m.债务人,
                    COALESCE(SUM(m.累计处置金额), 0) AS 累计处置金额,
                    COALESCE(SUM(m.累计现金回款), 0) AS 累计现金回款,
                    m.是否涉诉,
                    CASE WHEN COALESCE(MIN(r.本月末债权余额), 0) = 0 THEN '是' ELSE '否' END AS 是否完全处置,
                    COALESCE(SUM(m.上月处置金额), 0) AS 上月处置金额,
                    COALESCE(SUM(m.上月现金回款), 0) AS 上月现金回款,
                    m.期间,
                    m.管理公司,
                    GROUP_CONCAT(DISTINCT NULLIF(m.备注, '') SEPARATOR '; ') AS 备注
                  FROM 合并数据 m
                  LEFT JOIN 减值数据 r
                    ON m.债权人 = r.债权人
                    AND m.债务人 = r.债务人
                    AND m.是否涉诉 = r.是否涉诉
                    AND m.期间 = r.期间
                  GROUP BY m.债权人, m.债务人, m.是否涉诉, m.期间, m.管理公司
                  ORDER BY m.债权人, m.债务人;
                  """;

            // 参数绑定示例（Java）
            params.add(year);        // 年份，用于1到当月汇总
            params.add(month);       // 月份，用于1到当月汇总
            params.add(year);        // 年份，用于1到上月汇总
            params.add(month - 1);   // 上月（用于处置数据对比）
            params.add(year);        // 减值准备表年份
            params.add(month);       // 减值准备表月份

        } else if ("表9-新增逾期债权明细表".equals(tableName)) {
            // 新增表和处置表连接查询的SQL，并从减值准备表获取上年年末数据作为年初余额
            sql = """
                    WITH new_debt_data AS (
                      SELECT
                          债权人,
                          债务人,
                          是否涉诉,
                          期间,
                          科目名称,
                          债权性质,
                          IFNULL(`1月`, 0) AS `1月`,
                          IFNULL(`2月`, 0) AS `2月`,
                          IFNULL(`3月`, 0) AS `3月`,
                          IFNULL(`4月`, 0) AS `4月`,
                          IFNULL(`5月`, 0) AS `5月`,
                          IFNULL(`6月`, 0) AS `6月`,
                          IFNULL(`7月`, 0) AS `7月`,
                          IFNULL(`8月`, 0) AS `8月`,
                          IFNULL(`9月`, 0) AS `9月`,
                          IFNULL(`10月`, 0) AS `10月`,
                          IFNULL(`11月`, 0) AS `11月`,
                          IFNULL(`12月`, 0) AS `12月`,
                          备注
                        FROM 新增表
                      WHERE 年份 = ?
                  ),
                  disposal_data AS (
                      SELECT
                          债权人,
                          债务人,
                          是否涉诉,
                          期间,
                          SUM(现金处置 + 分期还款 + 资产抵债 + 其他方式) AS 累计处置金额
                        FROM 处置表
                        WHERE 年份 = ?
                        GROUP BY 债权人, 债务人, 是否涉诉, 期间
                    ),
                  year_start_balance AS (
                      SELECT
                          债权人,
                          债务人,
                          是否涉诉,
                          本月末债权余额 AS 年初余额
                        FROM 减值准备表
                        WHERE 年份 = ? - 1 AND 月份 = 12
                    )
                    SELECT
                      nd.债权人,
                      nd.债务人,
                      nd.是否涉诉,
                      -- 动态设置期间类型，根据年初余额是否为0来确定
                      CASE
                        WHEN IFNULL(ysb.年初余额, 0) = 0 THEN '2025年新增债权'
                        ELSE '存量新增债权'
                      END AS 期间,
                      IFNULL(ysb.年初余额, 0) AS 年初余额,
                      nd.`1月`,
                      nd.`2月`,
                      nd.`3月`,
                      nd.`4月`,
                      nd.`5月`,
                      nd.`6月`,
                      nd.`7月`,
                      nd.`8月`,
                      nd.`9月`,
                      nd.`10月`,
                      nd.`11月`,
                      nd.`12月`,
                      nd.科目名称,
                      nd.债权性质,
                      IFNULL(dd.累计处置金额, 0) AS 累计处置金额,
                      nd.备注
                    FROM new_debt_data nd
                  LEFT JOIN disposal_data dd ON
                      nd.债权人 = dd.债权人 AND
                      nd.债务人 = dd.债务人 AND
                      nd.是否涉诉 = dd.是否涉诉 AND
                      nd.期间 = dd.期间
                  LEFT JOIN year_start_balance ysb ON
                      nd.债权人 = ysb.债权人 AND
                      nd.债务人 = ysb.债务人 AND
                      nd.是否涉诉 = ysb.是否涉诉
                  WHERE
                      (nd.`1月` <> 0 OR
                       nd.`2月` <> 0 OR
                       nd.`3月` <> 0 OR
                       nd.`4月` <> 0 OR
                       nd.`5月` <> 0 OR
                       nd.`6月` <> 0 OR
                       nd.`7月` <> 0 OR
                       nd.`8月` <> 0 OR
                       nd.`9月` <> 0 OR
                       nd.`10月` <> 0 OR
                       nd.`11月` <> 0 OR
                       nd.`12月` <> 0)
                  ORDER BY nd.债权人, nd.债务人, nd.是否涉诉
                  """;

            // 添加参数
            params.add(year);         // 新增表年份
            params.add(year);         // 处置表年份
            params.add(year - 1);     // 减值准备表上一年年份

        } else {
            // 表3、表4和表5的sql语句
            sql = "SELECT * FROM " + tableName + " WHERE 年份 = ? AND 月份 = ?";
            params.add(year);
            params.add(month);
        }


        return new AbstractMap.SimpleEntry<>(sql, params);
    }

    /**
     * 将数据库查询结果 (`ResultSet`) 数据插入到指定的 Excel 工作表 (`Worksheet`)。
     * 该方法根据“期间”列定位目标行，复制格式，并将数据写入 Excel。
     *
     * @param year  数据对应的年份
     * @param month 数据对应的月份
     * @param sheet 目标 Excel 工作表 (`Worksheet`)，用于写入数据
     * @param rs    数据库查询结果 (`ResultSet`)，包含需要导出的数据
     * @throws Exception 如果数据处理或写入 Excel 过程中发生异常
     */
    private void insertDataToExcel(String databaseTableName, int year, int month, Worksheet sheet, ResultSet rs) throws Exception {
        Cells cells = sheet.getCells();

        // 获取当前年份和月份对应的列映射
        AbstractMap.SimpleEntry<Map<String, String>, int[]> monthMapping = getColumnSet(year, month, rs);
        int numColumns = 0;
        // 遍历查询结果，将数据写入 Excel
        while (rs.next()) {
            // （1）根据“期间”列，确定要写入 Excel 的目标行
            String periodValue = rs.getString("期间");
            if (periodValue == null || periodValue.isEmpty()) {
                // 期间为空，跳过
                continue;
            }
            // 获取目标行
            int targetRow;
            if ("表9-新增逾期债权明细表".equals(databaseTableName)) {
                targetRow = findTargetRowByPeriod(databaseTableName, cells, periodValue);
            } else {
                targetRow = findTargetRowByPeriod(cells, periodValue);
            }


            // 判断是否需要向上复制一行（用于格式保持）
            copyRowFormatUp(cells, targetRow);
            // 将数据库中的有效列数据写入 Excel
            insertRowData(databaseTableName, cells, targetRow, rs, monthMapping);
            numColumns++;
        }
    }

    /**
     * 生成上月数据列的映射表，并返回上月的年份和月份信息。
     *
     * @param year  当前年份
     * @param month 当前月份
     * @param rs    查询结果集 (ResultSet)
     * @return 一个包含两部分的 `AbstractMap.SimpleEntry`：
     * - `Map<String, String>`: 包含 "上月" -> "X月" 的映射 (如 "上月" -> "10月")
     * - `int[]`: 存储上月的年份和月份，例如 `[2024, 10]`
     * @throws SQLException 如果在处理 `ResultSet` 时发生 SQL 异常
     */
    private static AbstractMap.SimpleEntry<Map<String, String>, int[]> getColumnSet(int year, int month, ResultSet rs) throws SQLException {
        int[] prevYearAndMonth = getPreviousYearAndMonth(year, month);
        // 上月的月份数
        String prevMonthStr = prevYearAndMonth[1] + "月";

        Set<String> columnSet = getResultSetColumns(rs);
        Map<String, String> monthMapping = new HashMap<>();
        if (columnSet.contains(prevMonthStr)) {
            monthMapping.put("上月", prevMonthStr);
        }

        return new AbstractMap.SimpleEntry<>(monthMapping, prevYearAndMonth);
    }

    /**
     * 根据“期间”列的值，决定要写入 Excel 的哪一行。
     * 你需要根据模板中“2022年430年小计”、“2023年新增”、“2024年新增”等行
     * 自行编写匹配逻辑。此处仅演示如何简单返回某个行号。
     *
     * @param cells       工作表
     * @param periodValue 期间（来自数据库）
     * @return Excel 中对应的行号
     */
    private int findTargetRowByPeriod(Cells cells, String periodValue) throws Exception {
        // 默认起始行
        int startRow = 4;
        // Excel 第二列（索引从 0 开始）
        int searchColumn = 1;

        try {
            switch (periodValue) {
                case "2022年430债权":
                    break;
                case "2022年新增债权":
                    startRow = findRowByTextOrThrow(cells, "2022年430新增小计", searchColumn) + 1;
                    break;
                case "2023年新增债权":
                    startRow = findRowByTextOrThrow(cells, "2022年新增小计", searchColumn) + 1;
                    break;
                case "2024年新增债权":
                    startRow = findRowByTextOrThrow(cells, "2023年新增小计", searchColumn) + 1;
                    break;
                case "2025年新增债权":
                    startRow = findRowByTextOrThrow(cells, "2024年新增小计", searchColumn) + 1;
                    break;
                case "存量新增债权":
                    break;
                default:
                    throw new IllegalArgumentException("未知的期间值: " + periodValue);
            }
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Excel 结构异常: " + e.getMessage(), e);
        } catch (Exception e) {
            throw new Exception("查找目标行时发生错误，期间值: " + periodValue, e);
        }

        return findFirstEmptyRow(cells, startRow, searchColumn);
    }

    private int findTargetRowByPeriod(String tableName, Cells cells, String periodValue) throws Exception {
        // 默认起始行
        int startRow = 4;
        // Excel 第二列（索引从 0 开始）
        int searchColumn = 1;
        try {
            if ("表9-新增逾期债权明细表".equals(tableName)) {
                switch (periodValue) {
                    case "存量新增债权":
                        break;
                    case "2025年新增债权":
                        startRow = findRowByTextOrThrow(cells, "存量新增小计", searchColumn) + 1;
                        break;
                    default:
                        throw new IllegalArgumentException("未知的期间值: " + periodValue);
                }
            }
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Excel 结构异常: " + e.getMessage(), e);
        } catch (Exception e) {
            throw new Exception("查找目标行时发生错误，期间值: " + periodValue, e);
        }

        return findFirstEmptyRow(cells, startRow, searchColumn);
    }

    /**
     * 在 Excel 中查找指定文本所在的行，若找不到则抛出异常
     *
     * @param cells        Excel 单元格对象
     * @param searchText   要查找的文本
     * @param searchColumn 要查找的列索引
     * @return 目标文本所在行号
     * @throws Exception 如果找不到文本，则抛出异常
     */
    private int findRowByTextOrThrow(Cells cells, String searchText, int searchColumn) throws Exception {
        int rowIndex = findRowByText(cells, searchText, searchColumn);
        if (rowIndex == -1) {
            throw new Exception("未在 Excel 中找到文本: " + searchText);
        }
        return rowIndex;
    }

    /**
     * 在 Excel 中查找指定文本所在的行
     *
     * @param cells        Excel 单元格对象
     * @param searchText   要查找的文本
     * @param searchColumn 要查找的列索引
     * @return 目标文本所在行号，如果找不到返回-1
     */
    private int findRowByText(Cells cells, String searchText, int searchColumn) {
        int maxRows = cells.getMaxDataRow();
        for (int row = 0; row <= maxRows; row++) {
            Cell cell = cells.get(row, searchColumn);
            if (cell != null && cell.getValue() != null) {
                String cellValue = cell.getStringValue().trim();
                if (searchText.equals(cellValue)) {
                    return row;
                }
            }
        }
        return -1;
    }



    /**
     * 从指定起始行查找第一个空单元格的行号
     *
     * @param cells    Excel 单元格对象
     * @param startRow 起始行
     * @param colIndex 搜索的列索引（0 开始）
     * @return 返回第一个空行的行号
     */
    private int findFirstEmptyRow(Cells cells, int startRow, int colIndex) {
        int maxRows = cells.getMaxDataRow();
        for (int row = startRow; row <= maxRows; row++) {
            Cell cell = cells.get(row, colIndex);
            if (cell == null || cell.getValue() == null || cell.getStringValue().trim().isEmpty()) {
                return row;
            }
        }
        // 若没有空行，返回新行
        return maxRows + 1;
    }

    /**
     * 复制上一行的格式到当前行（如果当前行下没有空行）
     *
     * @param cells     Excel 工作表
     * @param targetRow 目标行（需要复制格式的行）
     */
    private void copyRowFormatUp(Cells cells, int targetRow) throws Exception {
        if (targetRow <= 0) {
            return;
        }
        // 获取目标行第二列（索引1）的单元格
        Cell targetCell = cells.get(targetRow + 1, 1);
        // 如果目标单元格不为空，则复制上一行的格式到目标行
        if (targetCell != null && targetCell.getValue() != null) {
            // 插入一行到 targetRow 位置（即在当前行上方插入新行）
            cells.insertRow(targetRow);
            // 复制当前行（原来的当前行现在下移为 targetRow+1）的格式和内容到新插入的行 targetRow
            cells.copyRow(cells, targetRow + 1, targetRow);
        }
    }

    /**
     * 将数据库中的有效列数据写入 Excel 行。
     * 如果数据库不存在某列，则跳过不写。
     *
     * @param cells             单元格对象
     * @param rowIndex          写入的行号
     * @param rs                当前结果集行
     * @param monthMappingEntry 月份映射
     * @throws SQLException SQL 异常
     */
    private void insertRowData(
            String databaseTableName,
            Cells cells,
            int rowIndex,
            ResultSet rs,
            AbstractMap.SimpleEntry<Map<String, String>, int[]> monthMappingEntry
                              ) throws Exception {
        //        读取数据库表名

        // 读取 YAML 配置中的列映射
        @SuppressWarnings("unchecked")
        Map<String, Integer> columnMapping = (Map<String, Integer>) readSingleLevelYaml("columnmapping.yaml", databaseTableName);

        // 获取列名映射和上月的年份、月份
        assert columnMapping != null;
        Map<String, String> monthMapping = monthMappingEntry.getKey();

        // 插入序号
        insertSequenceNumber(cells, rowIndex);
        // 遍历 columnMapping，将数据填充到 Excel
        for (Map.Entry<String, Integer> entry : columnMapping.entrySet()) {
            String columnName = entry.getKey();
            int columnIndex = entry.getValue();
            // 获取目标单元格
            Cell cell = cells.get(rowIndex, columnIndex);

            // 处理数据映射（表3）
            String value;
            if ("上月".equals(columnName)) {
                String mappedColumn = monthMapping.get("上月");
                value = (mappedColumn != null) ? rs.getString(mappedColumn) : "0";
            } else {
                value = rs.getString(columnName);
            }

            // 特殊处理"本年度累计回收"字段，确保为null时显示为0
            if ("本年度累计回收".equals(columnName) || "累计清收".equals(columnName)) {
                if (value == null || value.isEmpty()) {
                    value = "0";
                }
            } else if ("0.00".equals(value)) {
                // 对于非本年度累计回收字段，如果值为0则不写入
                continue;
            }

            // 插入数据到 Excel
            putValueWithFormat(cell, value);
        }
    }

    /**
     * 在 Excel 第一列插入序号。
     * <p>
     * - 如果上方单元格为空或是合并单元格，则插入 "1"。<br>
     * - 如果上方单元格是字符串格式的数字，则基于其值 +1 后插入当前单元格。<br>
     * - 保持当前单元格的格式，不影响 Excel 的原始样式。<br>
     * </p>
     *
     * @param cells    Excel 工作表的单元格集合。
     * @param rowIndex 需要插入序号的行索引（从 0 开始）。
     * @throws Exception 如果发生 Excel 处理异常。
     */
    private void insertSequenceNumber(Cells cells, int rowIndex) throws Exception {
        // 当前单元格
        Cell currentCell = cells.get(rowIndex, 0);
        // 上方单元格
        Cell aboveCell = (rowIndex > 0) ? cells.get(rowIndex - 1, 0) : null;
        // 默认值
        String newValue = "1";
        // 检查上方是否为合并单元格
        boolean isMerged = (aboveCell != null && aboveCell.isMerged());

        if (aboveCell != null && !isMerged) {
            // 获取上方单元格的值（去除空格）
            String aboveValue = aboveCell.getStringValue().trim();
            // 判断是否是纯数字字符串
            if (aboveValue.matches("\\d+")) {
                int sequence = Integer.parseInt(aboveValue) + 1;
                newValue = String.valueOf(sequence);
            }
        }
        // 插入序号
        currentCell.putValue(newValue);
    }

    /**
     * 仅用于内存分组的复合键：债权人 + 债务人 + 是否涉诉
     */
    private static class CompoundKey {
        final String creditor, debtor, isLitigation;

        CompoundKey(String c, String d, String l) {
            creditor = c;
            debtor = d;
            isLitigation = l;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (!(o instanceof CompoundKey k)) return false;
            return creditor.equals(k.creditor)
                   && debtor.equals(k.debtor)
                   && isLitigation.equals(k.isLitigation);
        }

        @Override
        public int hashCode() {
            return Objects.hash(creditor, debtor, isLitigation);
        }
    }

    /**
     * 缓存每条数据行的明细：
     * - creditor/debtor/isLitigation：分组要素
     * - caseName：首条记录的“案件名称”
     * - monthlyBalance：1~12 月余额
     * - cumulativeClear：当年累计清收处置
     */
    private static class DataRow {
        final String creditor, debtor, caseName, isLitigation;
        final Map<Integer, BigDecimal> monthlyBalance = new HashMap<>();
        BigDecimal cumulativeClear = BigDecimal.ZERO;

        DataRow(String c, String d, String n, String l) {
            creditor = c;
            debtor = d;
            caseName = n;
            isLitigation = l;
        }
    }

}
