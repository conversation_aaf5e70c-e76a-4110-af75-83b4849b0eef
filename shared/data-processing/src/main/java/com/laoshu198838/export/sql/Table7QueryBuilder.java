package com.laoshu198838.export.sql;

import org.springframework.stereotype.Component;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 表7专用SQL查询构建器
 * 10万元及以下应收债权明细表
 * 
 * <AUTHOR>
 */
@Component
public class Table7QueryBuilder implements QueryBuilder {
    
    @Override
    public String buildSql(String tableName, int year, int month, Map<String, Object> params) {
        return """
              WITH filtered_data AS (
                  -- 筛选出上年12月和当年1月至指定月份的数据，且债权余额≤10万
                  SELECT
                      债权人,
                      债务人,
                      是否涉诉,
                      期间,
                      年份,
                      月份,
                      本月末债权余额 AS 月末余额,
                      本年度累计回收
                  FROM 逾期债权数据库.减值准备表
                  WHERE ((年份 = ? - 1 AND 月份 = 12) OR (年份 = ? AND 月份 BETWEEN 1 AND ?))
                    AND 本月末债权余额 > 0           -- 大于0的余额
                    AND 本月末债权余额 <= 10  -- 10万元限制
              ),
              init_balance AS (
                  -- 计算年初余额（上年12月的余额）
                  SELECT
                      债权人,
                      债务人,
                      是否涉诉,
                      SUM(月末余额) AS 年初余额
                  FROM filtered_data
                  WHERE 年份 = ? - 1 AND 月份 = 12
                  GROUP BY 债权人, 债务人, 是否涉诉
              ),
              current_year_data AS (
                  -- 当年各月数据
                  SELECT
                      fd.债权人,
                      fd.债务人,
                      fd.是否涉诉,
                      fd.期间,
                      fd.月份,
                      fd.月末余额,    
                      fd.本年度累计回收,
                      -- 按债权人+债务人+是否涉诉分组，并按月份降序排序，取最大月份的累计回收
                      ROW_NUMBER() OVER (PARTITION BY fd.债权人, fd.债务人, fd.是否涉诉 ORDER BY fd.月份 DESC) AS month_rank
                  FROM filtered_data fd
                  WHERE fd.年份 = ?
              ),
              final_data AS (
                  -- 将当前年度数据与年初余额合并
                  SELECT
                      cyd.债权人,
                      cyd.债务人,
                      cyd.是否涉诉,
                      cyd.期间,
                      cyd.月份,
                      cyd.月末余额,
                      ib.年初余额,
                      -- 只保留最大月份的累计回收
                      CASE WHEN cyd.month_rank = 1 THEN cyd.本年度累计回收 ELSE NULL END AS 本年度累计回收,
                      -- 如果年初余额为0或NULL则标记为新增
                      CASE WHEN ib.年初余额 IS NULL OR ib.年初余额 = 0 THEN '是' ELSE '否' END AS 是否新增
                  FROM current_year_data cyd
                  LEFT JOIN init_balance ib ON cyd.债权人 = ib.债权人 AND cyd.债务人 = ib.债务人 AND cyd.是否涉诉 = ib.是否涉诉
              ),
              pivoted_data AS (
                  -- 透视数据，为每个月创建单独的列
                  SELECT
                      fd.债权人,
                      fd.债务人,
                      fd.是否涉诉,
                      fd.期间,
                      -- 对于非聚合列，使用聚合函数处理
                      MAX(fd.年初余额) AS 年初余额,
                      MAX(fd.本年度累计回收) AS 本年度累计回收,
                      MAX(fd.是否新增) AS 是否新增,
                      -- 动态创建1月到12月的列（只有当月份匹配时才返回余额值）
                      MAX(CASE WHEN fd.月份 = 1 THEN fd.月末余额 ELSE NULL END) AS '1月',
                      MAX(CASE WHEN fd.月份 = 2 THEN fd.月末余额 ELSE NULL END) AS '2月',
                      MAX(CASE WHEN fd.月份 = 3 THEN fd.月末余额 ELSE NULL END) AS '3月',
                      MAX(CASE WHEN fd.月份 = 4 THEN fd.月末余额 ELSE NULL END) AS '4月',
                      MAX(CASE WHEN fd.月份 = 5 THEN fd.月末余额 ELSE NULL END) AS '5月',
                      MAX(CASE WHEN fd.月份 = 6 THEN fd.月末余额 ELSE NULL END) AS '6月',
                      MAX(CASE WHEN fd.月份 = 7 THEN fd.月末余额 ELSE NULL END) AS '7月',
                      MAX(CASE WHEN fd.月份 = 8 THEN fd.月末余额 ELSE NULL END) AS '8月',
                      MAX(CASE WHEN fd.月份 = 9 THEN fd.月末余额 ELSE NULL END) AS '9月',
                      MAX(CASE WHEN fd.月份 = 10 THEN fd.月末余额 ELSE NULL END) AS '10月',
                      MAX(CASE WHEN fd.月份 = 11 THEN fd.月末余额 ELSE NULL END) AS '11月',
                      MAX(CASE WHEN fd.月份 = 12 THEN fd.月末余额 ELSE NULL END) AS '12月'
                  FROM final_data fd
                  GROUP BY fd.债权人, fd.债务人, fd.是否涉诉, fd.期间
              )
              -- 最终查询结果
              SELECT
                  债权人,
                  债务人,
                  是否涉诉,
                  期间,
                  COALESCE(年初余额, 0) AS 年初余额,
                  COALESCE(年初余额, 0) AS 截至2024年底应收余额,
                  `1月`, `2月`, `3月`, `4月`, `5月`, `6月`, 
                  `7月`, `8月`, `9月`, `10月`, `11月`, `12月`,
                  本年度累计回收 AS 累计清收,
                  是否新增
              FROM pivoted_data
              WHERE 
                  -- 确保各月份的值（如果不为Null）都小于等于10万元
                  (COALESCE(`1月`, 0) <= 10) AND
                  (COALESCE(`2月`, 0) <= 10) AND
                  (COALESCE(`3月`, 0) <= 10) AND
                  (COALESCE(`4月`, 0) <= 10) AND
                  (COALESCE(`5月`, 0) <= 10) AND
                  (COALESCE(`6月`, 0) <= 10) AND
                  (COALESCE(`7月`, 0) <= 10) AND
                  (COALESCE(`8月`, 0) <= 10) AND
                  (COALESCE(`9月`, 0) <= 10) AND
                  (COALESCE(`10月`, 0) <= 10) AND
                  (COALESCE(`11月`, 0) <= 10) AND
                  (COALESCE(`12月`, 0) <= 10) AND
                  -- 至少一个月份的值大于0
                  (COALESCE(`1月`, 0) > 0 OR
                   COALESCE(`2月`, 0) > 0 OR
                   COALESCE(`3月`, 0) > 0 OR
                   COALESCE(`4月`, 0) > 0 OR
                   COALESCE(`5月`, 0) > 0 OR
                   COALESCE(`6月`, 0) > 0 OR
                   COALESCE(`7月`, 0) > 0 OR
                   COALESCE(`8月`, 0) > 0 OR
                   COALESCE(`9月`, 0) > 0 OR
                   COALESCE(`10月`, 0) > 0 OR
                   COALESCE(`11月`, 0) > 0 OR
                   COALESCE(`12月`, 0) > 0)
              ORDER BY 债权人, 债务人, 是否涉诉
              """;
    }
    
    @Override
    public List<Object> buildParameters(String tableName, int year, int month, Map<String, Object> params) {
        List<Object> parameters = new ArrayList<>();
        parameters.add(year); // 上一年
        parameters.add(year);     // 当前年
        parameters.add(month);    // 截止月份
        parameters.add(year); // 上一年（用于年初余额计算）
        parameters.add(year);     // 当前年（用于当年数据筛选）
        return parameters;
    }
    
    @Override
    public boolean supports(String tableName) {
        return "表7-10万元及以下应收债权明细表".equals(tableName);
    }
}