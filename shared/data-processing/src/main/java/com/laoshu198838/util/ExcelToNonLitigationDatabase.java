package com.laoshu198838.util;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.Date;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.sql.Types;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import com.aspose.cells.Cell;
import com.aspose.cells.CellValueType;
import com.aspose.cells.Cells;
import com.aspose.cells.Row;
import com.aspose.cells.Workbook;
import com.aspose.cells.Worksheet;
import static com.laoshu198838.util.database.DatabaseUtils.getConnection;

/**
 * <AUTHOR>
 */
@Deprecated
public class ExcelToNonLitigationDatabase {

    public static void main(String[] args) {
        String databaseName = "逾期债权数据库";
        String excelFilePath = "/Users/<USER>/Library/CloudStorage/OneDrive-个人/08.程序/FinancialSystem/mysql_data/src/main/resources/2024年每月处置金额.xlsx";
        // 数据库URL、用户名、密码
        String jdbcUrl = "***************************/" + databaseName + "?useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=UTC";
        String jdbcUser = "root";
        String jdbcPassword = "Zlb&198838";

        try {
            Connection connection = getConnection(databaseName);
            assert connection != null;
            importDataToNonLitigationTable(connection);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // 更新数据库函数，此处略...

    // 检查并添加缺失的列
    public static void checkAndAddMissingColumns(Connection connection, String tableName) throws Exception {
        List<String> columnsToCheck = Arrays.asList("债权人", "债务人", "是否涉诉", "期间");

        for (String column : columnsToCheck) {
            if (!isColumnExists(connection, tableName, column)) {
                String createColumnQuery = "ALTER TABLE " + tableName + " ADD COLUMN `" + column + "` VARCHAR(255)";
                try (Statement stmt = connection.createStatement()) {
                    stmt.executeUpdate(createColumnQuery);
                    System.out.println("列 " + column + " 不存在，已创建该列。");
                }
            }
        }
    }

    // 检查数据库表中是否存在指定列
    public static boolean isColumnExists(Connection connection, String tableName, String columnName) throws Exception {
        String query = "SHOW COLUMNS FROM " + tableName + " LIKE ?";
        try (PreparedStatement stmt = connection.prepareStatement(query)) {
            stmt.setString(1, columnName);
            ResultSet resultSet = stmt.executeQuery();
            return resultSet.next();
        }
    }

    /**
     * 导入 Excel 数据到非诉讼表
     * <p>
     * 该方法采用事务控制，确保数据批量插入操作要么全部成功，要么全部失败回滚。
     * </p>
     *
     * @param conn 数据库连接对象
     * @throws Exception 操作失败时抛出异常
     */
    /**
     * 导入 Excel 数据到非诉讼表，并对重复的联合主键（债权人、债务人）记录进行合并：
     * <ul>
     *   <li>数值型字段进行求和</li>
     *   <li>债权名称字段用逗号连接</li>
     *   <li>其他字段采用首次出现的值</li>
     * </ul>
     *
     * 该方法使用事务控制，确保批量插入操作“全或无”，如有异常则回滚。
     *
     * @param conn 数据库连接对象
     * @throws Exception 操作失败时抛出异常
     */
    public static void importDataToNonLitigationTable(Connection conn) throws Exception {
        // 保存原始的自动提交状态
        boolean originalAutoCommit = conn.getAutoCommit();
        // 关闭自动提交，开启事务
        conn.setAutoCommit(false);

        PreparedStatement pstmt = null;
        Workbook workbook = null;

        try {
            // 1. Excel 文件路径
            String excelFilePath = "/Users/<USER>/Documents/08.program/FinancialSystem/mysql_data/src/main/resources/非诉表.xlsx";

            // 2. 加载 Excel 文件
            workbook = new Workbook(excelFilePath);
            Worksheet sheet = workbook.getWorksheets().get(0);  // 获取第一个工作表
            Cells cells = sheet.getCells();

            // 3. 假设第一行为表头，数据从第二行开始
            int firstDataRow = 1;
            int lastRow = cells.getMaxDataRow(); // 最后一行的索引

            // 4. 定义 Map 用于聚合同一联合主键（债权人-债务人）的记录，保持插入顺序
            Map<String, AggregatedRecord> recordMap = new LinkedHashMap<>();

            // 5. 遍历 Excel 行，并聚合数据
            for (int i = firstDataRow; i <= lastRow; i++) {
                Row row = cells.getRow(i);
                if (row == null) continue; // 跳过空行

                // 根据列索引逐个获取数据（Excel中的列顺序与数据库字段顺序保持一致）
                String 序号 = getAsposeCellString(cells.get(i, 0));
                String 债权人 = getAsposeCellString(cells.get(i, 1));
                String 债务人 = getAsposeCellString(cells.get(i, 2));
                String 债权名称 = getAsposeCellString(cells.get(i, 3));
                String 债权性质 = getAsposeCellString(cells.get(i, 4));
                Date 债权到期时间 = getCellDate(cells.get(i, 5));
                BigDecimal 二零二二年四月三十日债权账面余额 = getAsposeCellDecimal(cells.get(i, 6));
                BigDecimal 上月末本金 = getAsposeCellDecimal(cells.get(i, 7));
                BigDecimal 上月末利息 = getAsposeCellDecimal(cells.get(i, 8));
                BigDecimal 上月末违约金 = getAsposeCellDecimal(cells.get(i, 9));
                BigDecimal 本月本金增减 = getAsposeCellDecimal(cells.get(i, 10));
                BigDecimal 本月利息增减 = getAsposeCellDecimal(cells.get(i, 11));
                BigDecimal 本月违约金增减 = getAsposeCellDecimal(cells.get(i, 12));
                BigDecimal 本月末本金 = getAsposeCellDecimal(cells.get(i, 13));
                BigDecimal 本月末利息 = getAsposeCellDecimal(cells.get(i, 14));
                BigDecimal 本月末违约金 = getAsposeCellDecimal(cells.get(i, 15));
                BigDecimal 下月回收预计 = getAsposeCellDecimal(cells.get(i, 16));
                BigDecimal 本年度回收目标 = getAsposeCellDecimal(cells.get(i, 17));
                BigDecimal 本年度累计回收 = getAsposeCellDecimal(cells.get(i, 18));
                String 安排措施 = getAsposeCellString(cells.get(i, 19));
                String 责任人 = getAsposeCellString(cells.get(i, 20));
                String 备注 = getAsposeCellString(cells.get(i, 21));
                String 逾期年限 = getAsposeCellString(cells.get(i, 22));
                String 债权类别 = getAsposeCellString(cells.get(i, 23));
                String 管理公司 = getAsposeCellString(cells.get(i, 24));
                String 期间 = getAsposeCellString(cells.get(i, 25));

                // 6. 以“债权人-债务人”作为联合主键（注意：此处根据实际情况修改组合字段）
                String key = 债权人 + "-" + 债务人+"-"+期间;

                if (!recordMap.containsKey(key)) {
                    // 第一次出现，构造新的聚合记录
                    AggregatedRecord record = new AggregatedRecord();
                    record.序号 = 序号;
                    record.债权人 = 债权人;
                    record.债务人 = 债务人;
                    record.债权名称 = 债权名称;
                    record.债权性质 = 债权性质;
                    record.债权到期时间 = 债权到期时间;
                    record.二零二二年四月三十日债权账面余额 = (二零二二年四月三十日债权账面余额 != null ? 二零二二年四月三十日债权账面余额 : BigDecimal.ZERO);
                    record.上月末本金 = (上月末本金 != null ? 上月末本金 : BigDecimal.ZERO);
                    record.上月末利息 = (上月末利息 != null ? 上月末利息 : BigDecimal.ZERO);
                    record.上月末违约金 = (上月末违约金 != null ? 上月末违约金 : BigDecimal.ZERO);
                    record.本月本金增减 = (本月本金增减 != null ? 本月本金增减 : BigDecimal.ZERO);
                    record.本月利息增减 = (本月利息增减 != null ? 本月利息增减 : BigDecimal.ZERO);
                    record.本月违约金增减 = (本月违约金增减 != null ? 本月违约金增减 : BigDecimal.ZERO);
                    record.本月末本金 = (本月末本金 != null ? 本月末本金 : BigDecimal.ZERO);
                    record.本月末利息 = (本月末利息 != null ? 本月末利息 : BigDecimal.ZERO);
                    record.本月末违约金 = (本月末违约金 != null ? 本月末违约金 : BigDecimal.ZERO);
                    record.下月回收预计 = (下月回收预计 != null ? 下月回收预计 : BigDecimal.ZERO);
                    record.本年度回收目标 = (本年度回收目标 != null ? 本年度回收目标 : BigDecimal.ZERO);
                    record.本年度累计回收 = (本年度累计回收 != null ? 本年度累计回收 : BigDecimal.ZERO);
                    record.安排措施 = 安排措施;
                    record.责任人 = 责任人;
                    record.备注 = 备注;
                    record.逾期年限 = 逾期年限;
                    record.债权类别 = 债权类别;
                    record.管理公司 = 管理公司;
                    record.期间 = 期间;

                    recordMap.put(key, record);
                } else {
                    // 已存在，进行合并操作
                    AggregatedRecord record = recordMap.get(key);
                    // 数值型字段求和（若原值为null则视为0）
                    record.二零二二年四月三十日债权账面余额 = record.二零二二年四月三十日债权账面余额.add(二零二二年四月三十日债权账面余额 != null ? 二零二二年四月三十日债权账面余额 : BigDecimal.ZERO);
                    record.上月末本金 = record.上月末本金.add(上月末本金 != null ? 上月末本金 : BigDecimal.ZERO);
                    record.上月末利息 = record.上月末利息.add(上月末利息 != null ? 上月末利息 : BigDecimal.ZERO);
                    record.上月末违约金 = record.上月末违约金.add(上月末违约金 != null ? 上月末违约金 : BigDecimal.ZERO);
                    record.本月本金增减 = record.本月本金增减.add(本月本金增减 != null ? 本月本金增减 : BigDecimal.ZERO);
                    record.本月利息增减 = record.本月利息增减.add(本月利息增减 != null ? 本月利息增减 : BigDecimal.ZERO);
                    record.本月违约金增减 = record.本月违约金增减.add(本月违约金增减 != null ? 本月违约金增减 : BigDecimal.ZERO);
                    record.本月末本金 = record.本月末本金.add(本月末本金 != null ? 本月末本金 : BigDecimal.ZERO);
                    record.本月末利息 = record.本月末利息.add(本月末利息 != null ? 本月末利息 : BigDecimal.ZERO);
                    record.本月末违约金 = record.本月末违约金.add(本月末违约金 != null ? 本月末违约金 : BigDecimal.ZERO);
                    record.下月回收预计 = record.下月回收预计.add(下月回收预计 != null ? 下月回收预计 : BigDecimal.ZERO);
                    record.本年度回收目标 = record.本年度回收目标.add(本年度回收目标 != null ? 本年度回收目标 : BigDecimal.ZERO);
                    record.本年度累计回收 = record.本年度累计回收.add(本年度累计回收 != null ? 本年度累计回收 : BigDecimal.ZERO);

                    // 债权名称字段用逗号拼接（若新值不为空）
                    if (债权名称 != null && !债权名称.trim().isEmpty()) {
                        if (record.债权名称 == null || record.债权名称.trim().isEmpty()) {
                            record.债权名称 = 债权名称;
                        } else {
                            record.债权名称 = record.债权名称 + "," + 债权名称;
                        }
                    }
                    // 其他字段均保留第一次的值，不作修改
                }
            }

            // 7. 准备插入语句
            String insertSql = "INSERT INTO 非诉讼表 (" +
                               "  序号, 债权人, 债务人, 债权名称, 债权性质, 债权到期时间, " +
                               "  `2022年4月30日债权账面余额`, `上月末本金`, `上月末利息`, `上月末违约金`, " +
                               "  `本月本金增减`, `本月利息增减`, `本月违约金增减`, `本月末本金`, `本月末利息`, `本月末违约金`, " +
                               "  `下月回收预计`, `本年度回收目标`, `本年度累计回收`, 安排措施, 责任人, 备注, " +
                               "  逾期年限, 债权类别, 管理公司, 期间" +
                               ") VALUES (" +
                               "  ?, ?, ?, ?, ?, ?, " +
                               "  ?, ?, ?, ?, " +
                               "  ?, ?, ?, ?, ?, ?, " +
                               "  ?, ?, ?, ?, ?, ?, " +
                               "  ?, ?, ?, ?" +
                               ")";
            pstmt = conn.prepareStatement(insertSql);

            // 8. 遍历聚合后的记录，并批量插入数据库
            for (AggregatedRecord record : recordMap.values()) {
                pstmt.setString(1, record.序号);
                pstmt.setString(2, record.债权人);
                pstmt.setString(3, record.债务人);
                pstmt.setString(4, record.债权名称);
                pstmt.setString(5, record.债权性质);
                if (record.债权到期时间 != null) {
                    pstmt.setDate(6, new java.sql.Date(record.债权到期时间.getTime()));
                } else {
                    pstmt.setNull(6, Types.DATE);
                }
                pstmt.setBigDecimal(7, record.二零二二年四月三十日债权账面余额);
                pstmt.setBigDecimal(8, record.上月末本金);
                pstmt.setBigDecimal(9, record.上月末利息);
                pstmt.setBigDecimal(10, record.上月末违约金);
                pstmt.setBigDecimal(11, record.本月本金增减);
                pstmt.setBigDecimal(12, record.本月利息增减);
                pstmt.setBigDecimal(13, record.本月违约金增减);
                pstmt.setBigDecimal(14, record.本月末本金);
                pstmt.setBigDecimal(15, record.本月末利息);
                pstmt.setBigDecimal(16, record.本月末违约金);
                pstmt.setBigDecimal(17, record.下月回收预计);
                pstmt.setBigDecimal(18, record.本年度回收目标);
                pstmt.setBigDecimal(19, record.本年度累计回收);
                pstmt.setString(20, record.安排措施);
                pstmt.setString(21, record.责任人);
                pstmt.setString(22, record.备注);
                pstmt.setString(23, record.逾期年限);
                pstmt.setString(24, record.债权类别);
                pstmt.setString(25, record.管理公司);
                pstmt.setString(26, record.期间);

                pstmt.addBatch();
            }

            // 9. 执行批量插入并提交事务
            pstmt.executeBatch();
            conn.commit();
        } catch (Exception e) {
            conn.rollback();
            throw e;
        } finally {
            if (pstmt != null) {
                try {
                    pstmt.close();
                } catch (Exception ex) {
                    // 忽略关闭异常
                }
            }
            if (workbook != null) {
                workbook.dispose();
            }
            conn.setAutoCommit(originalAutoCommit);
        }
    }

    /**
     * 内部类：用于聚合非诉讼记录
     */
    static class AggregatedRecord {
        String 序号;
        String 债权人;
        String 债务人;
        String 债权名称;
        String 债权性质;
        Date 债权到期时间;
        BigDecimal 二零二二年四月三十日债权账面余额;
        BigDecimal 上月末本金;
        BigDecimal 上月末利息;
        BigDecimal 上月末违约金;
        BigDecimal 本月本金增减;
        BigDecimal 本月利息增减;
        BigDecimal 本月违约金增减;
        BigDecimal 本月末本金;
        BigDecimal 本月末利息;
        BigDecimal 本月末违约金;
        BigDecimal 下月回收预计;
        BigDecimal 本年度回收目标;
        BigDecimal 本年度累计回收;
        String 安排措施;
        String 责任人;
        String 备注;
        String 逾期年限;
        String 债权类别;
        String 管理公司;
        String 期间;
    }

    /**
     * 将 Aspose.Cells 的单元格内容转换为字符串，若为空则返回空字符串。
     */
    private static String getAsposeCellString(Cell cell) {
        if (cell == null || cell.getValue() == null) {
            return "";
        }
        return cell.getStringValue().trim();
    }

    /**
     * 将 Aspose.Cells 的单元格内容转换为 BigDecimal，若无法转换则返回 BigDecimal.ZERO。
     */
    private static BigDecimal getAsposeCellDecimal(Cell cell) {
        if (cell == null || cell.getValue() == null) {
            return BigDecimal.ZERO;
        }
        try {
            int cellType = cell.getType();
            if (cellType == CellValueType.IS_NUMERIC) {
                double numericValue = cell.getDoubleValue();
                return BigDecimal.valueOf(numericValue);
            } else {
                String strValue = cell.getStringValue().trim();
                if (!strValue.isEmpty()) {
                    return new BigDecimal(strValue);
                }
            }
        } catch (Exception e) {
            // 如需调试，可打印 e.printStackTrace();
        }
        return BigDecimal.ZERO;
    }

    /**
     * 将 Aspose.Cells 的单元格内容转换为 java.sql.Date。
     * <p>
     * 如果单元格为日期类型（IS_DATE_TIME），直接转换；
     * 如果为字符串，则尝试转换中文格式（例如 "2024年12月31日" 转为 "2024-12-31"），
     * 最后使用 Date.valueOf() 转换，如解析失败则返回 null。
     * </p>
     */
    private static Date getCellDate(Cell cell) {
        if (cell == null || cell.getValue() == null) {
            return null;
        }
        try {
            int cellType = cell.getType();
            if (cellType == CellValueType.IS_DATE_TIME) {
                com.aspose.cells.DateTime asposeDateTime = cell.getDateTimeValue();
                java.util.Date utilDate = asposeDateTime.toDate();
                return new Date(utilDate.getTime());
            } else {
                String dateStr = getAsposeCellString(cell);
                if (!dateStr.isEmpty()) {
                    // 如果包含中文日期标识，则转换成标准格式
                    if (dateStr.contains("年")) {
                        dateStr = dateStr.replace("年", "-").replace("月", "-").replace("日", "").trim();
                    }
                    // 使用 Date.valueOf 要求格式为 "yyyy-MM-dd"
                    return Date.valueOf(dateStr);
                }
            }
        } catch (Exception e) {
            // 若 Date.valueOf 转换失败，则尝试使用 SimpleDateFormat 解析
            try {
                String dateStr = getAsposeCellString(cell);
                if (!dateStr.isEmpty()) {
                    // 尝试 "yyyy-MM-dd" 格式
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    java.util.Date utilDate = sdf.parse(dateStr);
                    return new Date(utilDate.getTime());
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        return null;
    }
}