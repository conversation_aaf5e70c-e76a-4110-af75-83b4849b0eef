package com.laoshu198838.util;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.Date;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.sql.Types;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.Map;

import com.aspose.cells.Cell;
import com.aspose.cells.CellValueType;
import com.aspose.cells.Cells;
import com.aspose.cells.CellsHelper;
import com.aspose.cells.Row;
import com.aspose.cells.Workbook;
import com.aspose.cells.Worksheet;
import static com.laoshu198838.util.database.DatabaseUtils.getConnection;

/**
 * ExcelToImpairmentDatabase 类
 * <p>
 * 该类示范如何将“减值准备表.xlsx”导入到数据库的“减值准备表”中。
 * 在导入时，若联合主键（债权人、债务人、期间、是否涉诉）重复，则进行合并：
 * <ul>
 *   <li>数值型字段：相加</li>
 *   <li>科目名称：逗号拼接</li>
 *   <li>其他字段：保留首次出现的值</li>
 * </ul>
 * </p>
 */
@Deprecated
public class ExcelToImpairmentDatabase {

    public static void main(String[] args) {
        // 1. 数据库名称、Excel 文件路径（请根据实际情况修改）
        String databaseName = "逾期债权数据库";
        String excelJianZhiPath = "/Users/<USER>/Documents/08.program/FinancialSystem/mysql_data/src/main/resources/减值准备表.xlsx";

        // 2. 获取数据库连接并执行导入
        try (Connection conn = getConnection(databaseName)) {
            if (conn == null) {
                System.err.println("数据库连接失败！");
                return;
            }
            importDataToJianZhiZhunBeiBiao(conn, excelJianZhiPath);
            System.out.println("减值准备表数据已成功导入并合并！");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 将 Excel 的“减值准备表.xlsx”导入到数据库的“减值准备表”中。
     * <p>
     * 若联合主键（债权人、债务人、期间、是否涉诉）出现重复，则对数值字段相加，
     * “科目名称”用逗号拼接，其它字段保留第一次出现的值。
     * </p>
     *
     * @param conn          数据库连接对象
     * @param excelFilePath Excel 文件完整路径
     * @throws Exception 读写或数据库操作失败时抛出
     */
    public static void importDataToJianZhiZhunBeiBiao(Connection conn, String excelFilePath) throws Exception {
        // 关闭自动提交，开启事务
        boolean originalAutoCommit = conn.getAutoCommit();
        conn.setAutoCommit(false);

        Workbook workbook = null;
        PreparedStatement pstmt = null;

        try {
            // 1. 加载 Excel
            workbook = new Workbook(excelFilePath);
            Worksheet sheet = workbook.getWorksheets().get(0);
            Cells cells = sheet.getCells();

            // 2. 假设第 1 行（索引 0）是表头，数据从第 2 行（索引 1）开始
            int firstDataRow = 1;
            int lastRow = cells.getMaxDataRow();

            // 3. 用 Map 聚合记录：key = (债权人 + 债务人 + 期间 + 是否涉诉)
            //    value = AggregatedJZRecord（用于累加数值字段、拼接科目名称等）
            Map<String, AggregatedJZRecord> recordMap = new LinkedHashMap<>();

            for (int i = firstDataRow; i <= lastRow; i++) {
                Row row = cells.getRow(i);
                if (row == null) continue;
               // 假设序号在第 0 列
                Cell xuHaoCell = cells.get(i, 0);
                String xuHao = getAsposeCellString(xuHaoCell);
                // 如果序号为空，跳过该行
                if (xuHao == null) continue;

                // 以下索引需与你的 Excel 列对应（从 0 开始）
                String 序号 = getAsposeCellString(cells.get(i, 0));
                String 债权人 = getAsposeCellString(cells.get(i, 1));
                String 债务人 = getAsposeCellString(cells.get(i, 2));
                String 案件名称 = getAsposeCellString(cells.get(i, 3));
                String 科目名称 = getAsposeCellString(cells.get(i, 4));
                BigDecimal 二零二二年四月三十日债权金额 = getAsposeCellDecimal(cells.get(i, 5));
                BigDecimal 本月末债权余额 = getAsposeCellDecimal(cells.get(i, 6));
                Date 初始计提日期 = getCellDate(cells.get(i, 7));
                BigDecimal 计提减值金额 = getAsposeCellDecimal(cells.get(i, 8));
                BigDecimal 上月末余额 = getAsposeCellDecimal(cells.get(i, 9));
                BigDecimal 本月增减 = getAsposeCellDecimal(cells.get(i, 10));
                BigDecimal 本月余额 = getAsposeCellDecimal(cells.get(i, 11));
                BigDecimal 本年度回收目标 = getAsposeCellDecimal(cells.get(i, 12));
                BigDecimal 本年度累计回收 = getAsposeCellDecimal(cells.get(i, 13));
                String 备注 = getAsposeCellString(cells.get(i, 14));
                String 管理公司 = getAsposeCellString(cells.get(i, 15));
                // 如果数据长度超过255，则截取前255个字符
                if (管理公司 != null && 管理公司.length() > 50) {
                    管理公司 = 管理公司.substring(0, 50);
                }
                String 期间 = getAsposeCellString(cells.get(i, 16));
                String 是否全额计提坏账 = getAsposeCellString(cells.get(i, 17));
                String 是否涉诉 = getAsposeCellString(cells.get(i, 18));

                // 4. 组合联合主键
                String key = 债权人 + "-" + 债务人 + "-" + 期间 + "-" + 是否涉诉;

                // 5. 聚合逻辑：若不存在则创建，若已存在则累加数值/拼接科目名称
                if (!recordMap.containsKey(key)) {
                    // 第一次出现，构造新的聚合记录
                    AggregatedJZRecord record = new AggregatedJZRecord();
                    record.序号 = 序号;
                    record.债权人 = 债权人;
                    record.债务人 = 债务人;
                    record.案件名称 = 案件名称;
                    record.科目名称 = 科目名称;
                    record.初始计提日期 = 初始计提日期;
                    record.二零二二年四月三十日债权金额 = safeValue(二零二二年四月三十日债权金额);
                    record.本月末债权余额 = safeValue(本月末债权余额);
                    record.计提减值金额 = safeValue(计提减值金额);
                    record.上月末余额 = safeValue(上月末余额);
                    record.本月增减 = safeValue(本月增减);
                    record.本月余额 = safeValue(本月余额);
                    record.本年度回收目标 = safeValue(本年度回收目标);
                    record.本年度累计回收 = safeValue(本年度累计回收);
                    record.备注 = 备注;
                    record.管理公司 = 管理公司;
                    record.期间 = 期间;
                    record.是否全额计提坏账 = 是否全额计提坏账;
                    record.是否涉诉 = 是否涉诉;

                    recordMap.put(key, record);
                } else {
                    // 已存在，进行合并
                    AggregatedJZRecord record = recordMap.get(key);

                    // 数值字段相加
                    record.二零二二年四月三十日债权金额 = record.二零二二年四月三十日债权金额.add(safeValue(二零二二年四月三十日债权金额));
                    record.本月末债权余额 = record.本月末债权余额.add(safeValue(本月末债权余额));
                    record.计提减值金额 = record.计提减值金额.add(safeValue(计提减值金额));
                    record.上月末余额 = record.上月末余额.add(safeValue(上月末余额));
                    record.本月增减 = record.本月增减.add(safeValue(本月增减));
                    record.本月余额 = record.本月余额.add(safeValue(本月余额));
                    record.本年度回收目标 = record.本年度回收目标.add(safeValue(本年度回收目标));
                    record.本年度累计回收 = record.本年度累计回收.add(safeValue(本年度累计回收));

                    // 科目名称用逗号拼接（若新值不为空）
                    if (科目名称 != null && !科目名称.trim().isEmpty()) {
                        if (record.科目名称 == null || record.科目名称.trim().isEmpty()) {
                            record.科目名称 = 科目名称;
                        } else {
                            record.科目名称 = record.科目名称 + "," + 科目名称;
                        }
                    }
                    // 其他字段（序号、初始计提日期、备注、管理公司等） 保留首次出现的值
                }
            }

            // 6. 准备 INSERT 语句
            //    注意：这里列顺序要与数据库表字段顺序匹配
            String insertSql = "INSERT INTO 减值准备表 (" +
                               "  序号, 债权人, 债务人, 案件名称,科目名称, `2022年4月30日债权金额`, " +
                               "  本月末债权余额, 计提减值金额, 初始计提日期, 上月末余额, 本月增减, " +
                               "  本月余额, 本年度回收目标, 本年度累计回收, 备注, 管理公司, " +
                               "  期间, 是否全额计提坏账, 是否涉诉" +
                               ") VALUES (?, ?, ?,?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

            pstmt = conn.prepareStatement(insertSql);

            // 7. 将聚合后的记录批量插入数据库
            for (AggregatedJZRecord record : recordMap.values()) {
                // 按照列顺序一一对应
                pstmt.setInt(1, Integer.parseInt(record.序号));
                pstmt.setString(2, record.债权人);
                pstmt.setString(3, record.债务人);
                pstmt.setString(4, record.案件名称);
                pstmt.setString(5, record.科目名称);
                pstmt.setBigDecimal(6, record.二零二二年四月三十日债权金额);
                pstmt.setBigDecimal(7, record.本月末债权余额);
                pstmt.setBigDecimal(8, record.计提减值金额);
                if (record.初始计提日期 != null) {
                    pstmt.setDate(9, new java.sql.Date(record.初始计提日期.getTime()));
                } else {
                    pstmt.setNull(9, Types.DATE);
                }
//                pstmt.setDate(9, record.初始计提日期);
                pstmt.setBigDecimal(10, record.上月末余额);
                pstmt.setBigDecimal(11, record.本月增减);
                pstmt.setBigDecimal(12, record.本月余额);
                pstmt.setBigDecimal(13, record.本年度回收目标);
                pstmt.setBigDecimal(14, record.本年度累计回收);
                pstmt.setString(15, record.备注);

                pstmt.setString(16, record.管理公司);
                pstmt.setString(17, record.期间);
                pstmt.setString(18, record.是否全额计提坏账);
                pstmt.setString(19, record.是否涉诉);

                pstmt.addBatch();
            }

            pstmt.executeBatch();
            conn.commit();

        } catch (Exception e) {
            // 出现异常，回滚
            conn.rollback();
            throw e;
        } finally {
            // 关闭资源，恢复自动提交
            if (pstmt != null) {
                try { pstmt.close(); } catch (Exception ex) { /* 忽略 */ }
            }
            if (workbook != null) {
                workbook.dispose();
            }
            conn.setAutoCommit(originalAutoCommit);
        }
    }

    // ======== 辅助方法 ========

    /**
     * 安全返回 BigDecimal 值，如果为 null 则返回 0
     */
    private static BigDecimal safeValue(BigDecimal val) {
        return (val != null) ? val : BigDecimal.ZERO;
    }

    /**
     * 将 Aspose.Cells 的单元格内容转换为字符串，若为空则返回 null。
     *
     * @param cell Aspose.Cells 的单元格对象
     * @return 单元格的字符串内容，若为空则返回 null
     */
    private static String getAsposeCellString(Cell cell) {
        if (cell == null || cell.getValue() == null) {
            return null;
        }
        int cellType = cell.getType();
        if (cellType == CellValueType.IS_NUMERIC) {
            // 转换为整型字符串（假设数据都是整数）
            return String.valueOf((int) cell.getDoubleValue());
        }
        String value = cell.getStringValue().trim();
        value = value.replaceAll("[\\p{C}]", "");
        return value.isEmpty() ? null : value;
    }

    /**
     * 将 Aspose.Cells 的单元格内容转换为 BigDecimal，若无法转换则返回 BigDecimal.ZERO。
     */
    private static BigDecimal getAsposeCellDecimal(Cell cell) {
        if (cell == null || cell.getValue() == null) {
            return BigDecimal.ZERO;
        }
        try {
            int cellType = cell.getType();
            if (cellType == CellValueType.IS_NUMERIC) {
                double numericValue = cell.getDoubleValue();
                return BigDecimal.valueOf(numericValue);
            } else {
                String strValue = cell.getStringValue().trim();
                if (!strValue.isEmpty()) {
                    return new BigDecimal(strValue);
                }
            }
        } catch (Exception e) {
            // 可打印异常进行调试
        }
        return BigDecimal.ZERO;
    }

    /**
     * 将 Aspose.Cells 的单元格内容转换为 java.sql.Date。
     * 支持：
     * 1) Excel 原生日期类型 (IS_DATE_TIME)
     * 2) Excel 以数值存储的日期 (IS_NUMERIC)
     * 3) 字符串格式的日期：支持 "yyyy-MM-dd", "yyyy/M/d", "yyyy年M月d日" 等
     * 4) 若无法解析，返回 null
     *
     * @param cell Aspose.Cells 单元格对象
     * @return java.sql.Date，若无法解析则返回 null
     */
    private static Date getCellDate(Cell cell) {
        if (cell == null || cell.getValue() == null) {
            return null;
        }
        try {
            int cellType = cell.getType();

            // 1. 如果单元格是 Excel 原生日期类型
            if (cellType == CellValueType.IS_DATE_TIME) {
                com.aspose.cells.DateTime asposeDateTime = cell.getDateTimeValue();
                return new Date(asposeDateTime.toDate().getTime());
            }

            // 2. 如果单元格是数值类型（Excel 可能用数值存储日期）
            if (cellType == CellValueType.IS_NUMERIC) {
                double numericValue = cell.getDoubleValue();
                // 如果数值为 0，则视为无效日期，返回 null
                if (numericValue == 0.0) {
                    return null;
                }
                // 否则再用 CellsHelper 转换
                boolean is1904 = cell.getWorksheet().getWorkbook().getSettings().getDate1904();
                com.aspose.cells.DateTime dt = CellsHelper.getDateTimeFromDouble(numericValue, is1904);
                return new Date(dt.toDate().getTime());
            }

            // 3. 如果是字符串类型，进行解析
            String dateStr = getAsposeCellString(cell);
            // 3.1 如果是 "0" 或空字符串，也返回 null
            if (dateStr == null || dateStr.isEmpty() || "0".equals(dateStr)) {
                return null;
            }

            // 3.2 如果包含中文“年”“月”“日”，替换成 “-”
            if (dateStr.contains("年")) {
                dateStr = dateStr.replace("年", "-")
                        .replace("月", "-")
                        .replace("日", "")
                        .trim();
            }
            // 3.3 将可能的斜杠 / 也统一替换成 “-”
            dateStr = dateStr.replace("/", "-").trim();

            // 3.4 先尝试 "yyyy-MM-dd"
            try {
                return Date.valueOf(dateStr); // 标准格式
            } catch (Exception e) {
                // 如果失败，再用 SimpleDateFormat 尝试 "yyyy-M-d"
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-M-d");
                    sdf.setLenient(false); // 严格模式
                    java.util.Date utilDate = sdf.parse(dateStr);
                    return new Date(utilDate.getTime());
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        // 如果都失败，返回 null
        return null;
    }

    /**
     * 检查并添加数据库中缺失的列（可选）
     */
    public static void checkAndAddMissingColumns(Connection connection, String tableName) throws Exception {
        for (String column : Arrays.asList("债权人", "债务人", "是否涉诉", "期间")) {
            if (!isColumnExists(connection, tableName, column)) {
                String sql = "ALTER TABLE " + tableName + " ADD COLUMN `" + column + "` VARCHAR(255)";
                try (Statement stmt = connection.createStatement()) {
                    stmt.executeUpdate(sql);
                    System.out.println("列 " + column + " 不存在，已创建。");
                }
            }
        }
    }

    /**
     * 判断表中是否存在指定列（可选）
     */
    public static boolean isColumnExists(Connection connection, String tableName, String columnName) throws Exception {
        String query = "SHOW COLUMNS FROM " + tableName + " LIKE ?";
        try (PreparedStatement stmt = connection.prepareStatement(query)) {
            stmt.setString(1, columnName);
            try (ResultSet rs = stmt.executeQuery()) {
                return rs.next();
            }
        }
    }

    // ======== 内部类：聚合记录，用于合并重复行 ========
    static class AggregatedJZRecord {
        // 序号（若数据库不做自动增长，可保留；若做自增可视情况处理）
        String 序号;

        // 关键字段
        String 债权人;
        String 债务人;
        String 期间;
        String 是否涉诉;

        // 其余字段
        String 案件名称;
        String 科目名称;
        BigDecimal 二零二二年四月三十日债权金额;
        BigDecimal 本月末债权余额;
        BigDecimal 计提减值金额;
        Date 初始计提日期;
        BigDecimal 上月末余额;
        BigDecimal 本月增减;
        BigDecimal 本月余额;
        BigDecimal 本年度回收目标;
        BigDecimal 本年度累计回收;
        String 备注;
        String 管理公司;
        String 是否全额计提坏账;
    }
}