package com.laoshu198838.util;

import com.aspose.cells.DateTime;

import java.sql.*;
import java.util.List;

import static com.laoshu198838.util.file.ExcelUtils.readExcelToList;

/**
 * <AUTHOR>
 */

public class insertDataUtil {

    /**
     * 批量插入数据
     *
     * @param databaseName 数据库名称
     * @param tableName    表名
     * @param columns      插入的列名 (例如 "id, name, email")
     * @param data         要插入的数据，每一行是一个 List<Object>
     */
    public static void batchInsert(
            String databaseName,
            String tableName,
            String columns,
            List<List<Object>> data
                                  ) {
        // 动态生成 SQL 占位符部分
        StringBuilder placeholders = new StringBuilder();
        String[] columnArray = columns.split(",");

        // 生成 VALUES 部分的占位符，例如 (?, ?, ?)
        for (int i = 0; i < columnArray.length; i++) {
            placeholders.append("?");
            if (i < columnArray.length - 1) {
                placeholders.append(", ");
            }
        }

        // 构造 ON DUPLICATE KEY UPDATE 子句
        StringBuilder updateClause = new StringBuilder();
        for (String column : columnArray) {
            column = column.trim();

            // 指定需要累加的列
            if ("`1月`".equals(column) || "`2月`".equals(column) || "`3月`".equals(column) ||
                "`4月`".equals(column) || "`5月`".equals(column) || "`6月`".equals(column) ||
                "`7月`".equals(column) || "`8月`".equals(column) || "`9月`".equals(column) ||
                "`10月`".equals(column) || "`11月`".equals(column) || "`12月`".equals(column) ||
                "`年初逾期债权余额`".equals(column)) {
                // 构造累加逻辑
                updateClause.append(column).append(" = ").append(column).append(" + VALUES(").append(column).append("), ");
            }
        }

        // 移除最后多余的逗号和空格
        if (!updateClause.isEmpty()) {
            updateClause.setLength(updateClause.length() - 2);
        }

        // 构造完整 SQL
        String sql = String.format(
                "INSERT INTO %s (%s) VALUES (%s) ON DUPLICATE KEY UPDATE %s",
                tableName, columns, placeholders, updateClause
                                  );

        // 打印 SQL 用于调试
        System.out.println("Generated SQL: " + sql);

        try (Connection connection = DatabaseUtil.getConnection(databaseName);
             PreparedStatement preparedStatement = connection.prepareStatement(sql)) {

            // 遍历每一行数据，逐行设置参数
            for (List<Object> row : data) {
                for (int i = 0; i < row.size(); i++) {
                    Object value = row.get(i);

                    // 根据数据类型设置参数
                    if (value == null) {
                        // 设置为 SQL NULL
                        preparedStatement.setNull(i + 1, Types.NULL);
                    } else if (value instanceof DateTime asposeDateTime) {
                        // 处理 Aspose.Cells 的 DateTime 类型
                        preparedStatement.setTimestamp(i + 1, new Timestamp(asposeDateTime.toDate().getTime()));
                    } else if (value instanceof java.util.Date) {
                        // 处理标准 Java Date 类型
                        preparedStatement.setTimestamp(i + 1, new Timestamp(((java.util.Date) value).getTime()));
                    } else {
                        // 设置通用类型
                        preparedStatement.setObject(i + 1, value);
                    }
                }
                // 添加到批量操作中
                preparedStatement.addBatch();
            }

            // 执行批量插入
            int[] rowsAffected = preparedStatement.executeBatch();
            System.out.println("Rows inserted or updated: " + rowsAffected.length);

        } catch (SQLException e) {
            // 打印错误的 SQL 和异常信息，用于调试
            System.err.println("Failed SQL: " + sql);
            e.printStackTrace();
            throw new RuntimeException("Failed to batch insert data into " + tableName, e);
        }
    }

    public static void updateDebtData(
            String databaseName,
            String tableName,
            List<List<Object>> data
                                     ) {
        // 主键字段
        String[] primaryKeys = {"债权人", "债务人", "期间", "是否涉诉"};
        // 要更新的字段
        String updateField = "年初逾期债权余额";

        // 构造 INSERT INTO 语句
        StringBuilder sql = new StringBuilder("INSERT INTO ").append(tableName).append(" (");
        for (String key : primaryKeys) {
            sql.append("`").append(key).append("`, ");
        }
        sql.append("`").append(updateField).append("`) VALUES (");
        for (int i = 0; i < primaryKeys.length + 1; i++) {
            sql.append("?");
            if (i < primaryKeys.length) {
                sql.append(", ");
            }
        }
        sql.append(") ON DUPLICATE KEY UPDATE `").append(updateField)
                .append("` = `").append(updateField).append("` + VALUES(`").append(updateField).append("`)");

        System.out.println("Generated SQL: " + sql);

        try (Connection connection = DatabaseUtil.getConnection(databaseName);
             PreparedStatement preparedStatement = connection.prepareStatement(sql.toString())) {

            // 遍历数据
            for (List<Object> row : data) {
                if (row.size() != primaryKeys.length + 1) {
                    throw new IllegalArgumentException("数据列数必须为主键数 + 1");
                }

                // 设置参数
                for (int i = 0; i < row.size(); i++) {
                    preparedStatement.setObject(i + 1, row.get(i));
                }

                // 添加到批处理
                preparedStatement.addBatch();
            }

            // 执行批量操作
            int[] result = preparedStatement.executeBatch();
            System.out.println("更新成功：" + result.length + " 条记录");

        } catch (SQLException e) {
            e.printStackTrace();
            throw new RuntimeException("批量更新数据失败", e);
        }
    }

    private static Connection getConnection(String databaseName) throws SQLException {
        String url = "***************************/" + databaseName + "?useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=UTC";
        String username = "root";
        String password = "Zlb&198838";
        return DriverManager.getConnection(url, username, password);
    }

    public static void main(String[] args) {
        // 示例文件路径
        String filePath = "/Users/<USER>/Library/CloudStorage/OneDrive-个人/08.程序/FinancialSystem/mysql_data/src/main/resources/逾期债权期初金额.xlsx";

        // 指定工作表名称（可为空）
        // 如果为空，读取第一个工作表
        String sheetName = "表8-临3表";

        // 调用方法读取 Excel 数据
        List<List<Object>> excelData = readExcelToList(filePath, sheetName);

        // 输出读取的数据
        for (List<Object> row : excelData) {
            System.out.println(row);
        }

        // 数据库名称
        String databaseName = "逾期债权数据库";
        // 表名
        String tableName = "汇总表";
        // 列名
        String columns = "`债权人`, `债务人`, " +
                         "`期间`, `是否涉诉`, `年初逾期债权余额`";
        // 插入数据
        batchInsert(databaseName, tableName, columns, excelData);
    }
}